import { app, shell, BrowserWindow, ipcMain, systemPreferences } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'

// 应用图标路径
const iconPath = is.dev
  ? join(__dirname, '../../resources/icon.png')
  : join(process.resourcesPath, 'icon.png')
import { privacyProtection } from './utils/PrivacyProtection'
import { GoogleGenAI } from '@google/genai'
import { getSystemPrompt } from './prompts'
import { analyzeAudioBuffer, saveDebugAudio } from './audioUtils'
import { spawn, ChildProcess } from 'child_process'
import { exec } from 'child_process'
import { promisify } from 'util'
// 导入服务管理器
import { serviceManager } from './services'
// 导入音频流管理器
import { ImprovedStreamManager, StreamManagerConfig } from './audio/ImprovedStreamManager'
// 导入自定义API管理器
import { customAPIManager } from './services/CustomAPIManager'

const execAsync = promisify(exec)

// 全局错误处理 - 防止EIO错误导致应用崩溃
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)

  // 特殊处理EIO错误
  if (error.message?.includes('EIO') || (error as any).code === 'EIO') {
    console.log('EIO error detected - likely a broken pipe or process communication issue')
    // 不退出应用，只记录错误
    return
  }

  // 其他严重错误才退出
  if (error.message?.includes('FATAL') || error.message?.includes('CRITICAL')) {
    console.error('Fatal error detected, exiting...')
    process.exit(1)
  }
})

process.on('unhandledRejection', (reason, promise) => {
  // 过滤掉Speechmatics的正常断开连接错误
  if (reason instanceof Error &&
      (reason.message.includes('Client network socket disconnected') ||
       reason.message.includes('ECONNRESET') ||
       (reason as any).code === 'ECONNRESET')) {
    console.log('ℹ️ Speechmatics connection closed (normal behavior during inactivity)')
    return
  }

  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  // 不退出应用，只记录错误
})

// 权限状态类型定义
interface PermissionStatus {
  granted: boolean
  canRequest: boolean
  message: string
}

interface SystemPermissions {
  screenRecording: PermissionStatus
  microphone: PermissionStatus
  apiKey: PermissionStatus
  audioDevice: PermissionStatus
}

let mainWindow: BrowserWindow | null = null
let floatingWindow: BrowserWindow | null = null
let geminiSession: any = null
let systemAudioProc: ChildProcess | null = null
let isInitializingSession = false
let reconnectAttempts = 0
let maxReconnectAttempts = 3
let reconnectTimeout: NodeJS.Timeout | null = null
let currentApiKey: string | null = ''
let currentCustomPrompt = ''
let currentProfile = 'interview'
let currentLanguage = 'cmn-CN' // Gemini Live API 支持的中文语言代码
let messageBuffer = '' // AI 回复缓冲区
let currentTranscription = '' // 当前转录缓冲区
let isAudioCaptureActive = false // 音频捕获状态标记
let isStartingAudioCapture = false // 防止并发启动音频捕获

// 🎯 动态音频配置系统
interface AudioConfig {
  sampleRate: number
  channels: number
  encoding: string
}

// 各转录API的音频格式要求
const TRANSCRIPTION_AUDIO_CONFIGS: Record<string, AudioConfig> = {
  'speechmatics': { sampleRate: 16000, channels: 1, encoding: 'pcm_s16le' },
  'deepgram': { sampleRate: 24000, channels: 1, encoding: 'linear16' },
  'gladia': { sampleRate: 16000, channels: 1, encoding: 'wav/pcm' },
  'default': { sampleRate: 16000, channels: 1, encoding: 'pcm_s16le' }
}

let currentAudioConfig: AudioConfig = TRANSCRIPTION_AUDIO_CONFIGS.default

/**
 * 根据转录服务更新音频配置
 */
function updateAudioConfigForTranscriptionService(provider: string): void {
  const config = TRANSCRIPTION_AUDIO_CONFIGS[provider.toLowerCase()] || TRANSCRIPTION_AUDIO_CONFIGS.default
  currentAudioConfig = config
  console.log(`🎯 Updated audio config for ${provider}:`, {
    sampleRate: config.sampleRate,
    channels: config.channels,
    encoding: config.encoding
  })
}
// 音频流管理器
let audioStreamManager: ImprovedStreamManager | null = null
let improvedStreamManager: ImprovedStreamManager | null = null
let isStreamManagerActive: boolean = false

/**
 * 初始化音频流管理器
 */
async function initializeAudioStreamManager(): Promise<boolean> {
  if (audioStreamManager) {
    console.log('🎵 Audio stream manager already initialized')
    return true
  }

  try {
    // 获取当前服务配置
    const streamConfig: StreamManagerConfig = {
      gemini: {
        apiKey: process.env.VITE_GEMINI_API_KEY || '',
        model: 'gemini-2.0-flash-exp',
        customPrompt: currentCustomPrompt,
        retryAttempts: 2,
        retryDelay: 2000
      }
    }

    audioStreamManager = new ImprovedStreamManager(streamConfig)

    // 设置事件监听
    setupAudioStreamEvents()

    // 启动管理器
    const started = await audioStreamManager.start()
    if (!started) {
      throw new Error('Failed to start audio stream manager')
    }

    console.log('🎵 Audio stream manager initialized successfully')
    return true

  } catch (error) {
    console.error('🎵 Failed to initialize audio stream manager:', error)
    audioStreamManager = null
    return false
  }
}

/**
 * 初始化改进的音频流管理器
 * 专门解决转录和AI回答不显示的问题
 */
async function initializeImprovedStreamManager(): Promise<boolean> {
  if (improvedStreamManager && isStreamManagerActive) {
    console.log(' Improved stream manager already initialized and active')
    return true
  }

  try {
    console.log(' Initializing improved stream manager...')

    // 停止旧的管理器
    if (improvedStreamManager) {
      await improvedStreamManager.stop()
      improvedStreamManager = null
    }

    // 获取当前配置
    const streamConfig: StreamManagerConfig = {
      gemini: {
        apiKey: currentApiKey || '',
        model: 'gemini-live-2.5-flash-preview',
        language: currentLanguage || 'cmn-CN',
        profile: currentProfile || 'interview',
        customPrompt: currentCustomPrompt || ''
      },
      queue: {
        maxAudioQueueSize: 2000,
        maxTranscriptionQueueSize: 500,
        maxAIResponseQueueSize: 100
      }
    }

    improvedStreamManager = new ImprovedStreamManager(streamConfig)

    // 设置事件监听
    setupImprovedStreamManagerEvents()

    // 启动管理器
    const started = await improvedStreamManager.start()
    if (!started) {
      throw new Error('Failed to start improved stream manager')
    }

    isStreamManagerActive = true
    console.log(' Improved stream manager initialized successfully')
    return true

  } catch (error) {
    console.error(' Failed to initialize improved stream manager:', error)
    improvedStreamManager = null
    isStreamManagerActive = false
    return false
  }
}

/**
 * 设置音频流事件监听
 */
function setupAudioStreamEvents(): void {
  if (!audioStreamManager) return

  audioStreamManager.on('transcription', (type, result) => {
    console.log(` Transcription from ${type}:`, result)
    sendToRenderer('transcription-result', { type, result })
  })

  audioStreamManager.on('consumer-error', (type, error) => {
    console.error(` Consumer error [${type}]:`, error)
    sendToRenderer('consumer-error', { type, error: error.message })
  })

  audioStreamManager.on('queue-full', () => {
    console.warn(' Audio queue is full, dropping data')
    sendToRenderer('audio-queue-full')
  })

  audioStreamManager.on('backpressure-changed', (active) => {
    if (active) {
      console.warn(' Audio queue backpressure activated')
    }
    sendToRenderer('audio-backpressure', active)
  })
}

/**
 * 设置改进流管理器事件监听
 */
function setupImprovedStreamManagerEvents(): void {
  if (!improvedStreamManager) return

  // 转录结果事件
  improvedStreamManager.on('transcription-result', (result) => {
    console.log(' Transcription result:', result.text)
    // 发送到前端 - 使用原有的事件名保持兼容性
    sendToRenderer('transcription-update', result.text)
  })

  // AI回复事件
  improvedStreamManager.on('ai-response', (response) => {
    console.log(' AI response:', response.text)
    // 发送到前端 - 使用原有的事件名保持兼容性
    sendToRenderer('ai-response', response.text)
  })

  // 连接状态事件
  improvedStreamManager.on('gemini-connected', () => {
    console.log(' Gemini connected via improved stream manager')
    sendToRenderer('update-status', 'Connected to Gemini - Starting recording...')
  })

  improvedStreamManager.on('gemini-disconnected', (reason) => {
    console.log(' Gemini disconnected:', reason)
    sendToRenderer('session-closed')
  })

  improvedStreamManager.on('gemini-error', (error) => {
    console.error(' Gemini error via improved stream manager:', error)
    sendToRenderer('session-error', `Gemini API 连接错误: ${error}`)
  })

  // 🐾 优化：separated模式音频处理事件（消除重复格式转换）
  improvedStreamManager.on('audio-chunk-ready', async (audioData) => {
    // 在separated模式下，将音频数据发送给当前的服务适配器
    try {
      const currentAdapter = serviceManager.getCurrentAdapter()
      if (currentAdapter && typeof currentAdapter.sendAudio === 'function') {
        // 🎯 优化：直接发送Buffer，避免Buffer→base64→Buffer的重复转换
        await (currentAdapter as any).sendAudio(audioData.data)
        // 减少日志频率，提升性能
        if (Math.random() < 0.01) {
          console.log('🎯 Audio Buffer sent to SeparatedAdapter directly, size:', audioData.data.length)
        }
      } else {
        console.warn('🐾⚠️ No audio processor available in separated mode')
      }
    } catch (error) {
      console.error('🐾❌ Error processing audio in separated mode:', error)
    }
  })

  // 队列状态事件
  improvedStreamManager.on('queue-overflow', (queueType) => {
    console.warn(` Queue overflow: ${queueType}`)
    sendToRenderer('audio-queue-full')
  })

  improvedStreamManager.on('metrics-updated', (metrics) => {
    // 定期发送队列指标到前端用于调试
    if (process.env.NODE_ENV === 'development') {
      sendToRenderer('queue-metrics', metrics)
    }
  })

  // 启动和停止事件
  improvedStreamManager.on('started', () => {
    console.log(' Improved stream manager started')
    sendToRenderer('stream-manager-started')
  })

  improvedStreamManager.on('stopped', () => {
    console.log(' Improved stream manager stopped')
    isStreamManagerActive = false
    sendToRenderer('stream-manager-stopped')
  })

  improvedStreamManager.on('error', (error) => {
    console.error(' Improved stream manager error:', error)
    isStreamManagerActive = false
    sendToRenderer('stream-manager-error', error.message)
  })
}

// 音频处理计数器（保留用于调试）
let audioChunkCount = 0

function createWindow(): void {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 700,
    show: false,
    autoHideMenuBar: true,
    resizable: false,
    title: 'GeekAssistant',
    // 应用图标设置
    icon: iconPath,
    // 隐私保护设置 - 防止屏幕共享检测
    skipTaskbar: false, // 主窗口保持在任务栏，看起来正常
    webPreferences: {
      preload: join(__dirname, '../preload/index.mjs'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true,
      // 防止内容被捕获
      webSecurity: true
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow?.show()

    // 应用隐私保护模块
    if (mainWindow) {
      privacyProtection.applyWindowProtection(mainWindow, false)

      // 延迟注入脚本，确保页面完全加载
      setTimeout(() => {
        privacyProtection.injectAntiDetectionScripts(mainWindow!, false)

        // 检查保护状态
        privacyProtection.checkProtectionStatus(mainWindow!).then(status => {
          console.log(' Main window protection status:', status ? 'Active' : 'Partial')
        })
      }, 500)
    }

    // 生产环境禁止打开DevTools
    disableDevTools(mainWindow!)
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 添加右键菜单打开开发者工具
  mainWindow.webContents.on('context-menu', (event, params) => {
    if (process.env.NODE_ENV === 'development') {
      event.preventDefault()
      mainWindow?.webContents.openDevTools()
    }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }


}

function createFloatingWindow(): BrowserWindow {
  floatingWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    resizable: true,
    movable: true,
    minimizable: false,
    maximizable: false,
    skipTaskbar: true,
    // 强化隐私保护设置
    show: false, // 初始隐藏
    focusable: false, // 不获取焦点，避免被检测
    hasShadow: false, // 无阴影，更隐蔽
    opacity: 0.95, // 略微透明，更难被发现
    webPreferences: {
      preload: join(__dirname, '../preload/index.mjs'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true,
      // 防止内容被捕获
      webSecurity: true,
      backgroundThrottling: false, // 防止后台节流影响功能
      offscreen: false // 确保正常渲染
    }
  })

  // 强化隐私保护 - 防止在屏幕共享中显示
  if (process.platform === 'darwin') {
    try {
      // @ts-ignore - macOS specific API
      floatingWindow.setWindowButtonVisibility?.(false)
      // @ts-ignore - 设置窗口级别，避免被捕获
      floatingWindow.setLevel?.('screen-saver')
      // @ts-ignore - 防止窗口被屏幕录制
      floatingWindow.setContentProtection?.(true)
      // @ts-ignore - 设置为隐私敏感窗口
      floatingWindow.setPrivacySensitive?.(true)
    } catch (error) {
      console.log(' macOS advanced privacy features not available:', error)
    }
  }

  // Windows 特定的强化隐私保护
  if (process.platform === 'win32') {
    try {
      // @ts-ignore - Windows specific API
      floatingWindow.setSkipTaskbar?.(true)
      // 设置窗口属性，避免被DWM捕获
      floatingWindow.hookWindowMessage?.(0x0024, () => {
        // WM_GETMINMAXINFO - 防止窗口信息被获取
        return
      })
    } catch (error) {
      console.log(' Windows advanced privacy features not available:', error)
    }
  }

  // 设置窗口为不可捕获类型
  try {
    // @ts-ignore - macOS specific API
    floatingWindow.setVisibleOnAllWorkspaces?.(true, { visibleOnFullScreen: true })
  } catch (error) {
    console.log(' macOS workspace API not available:', error)
  }

  // 浮动窗口ready-to-show事件 - 使用隐私保护模块
  floatingWindow.on('ready-to-show', () => {
    // 应用浮动窗口隐私保护
    if (floatingWindow) {
      privacyProtection.applyWindowProtection(floatingWindow, true)

      // 延迟显示和脚本注入，避免被检测
      setTimeout(() => {
        floatingWindow?.show()

        // 注入强化的反检测脚本
        privacyProtection.injectAntiDetectionScripts(floatingWindow!, true)

        // 检查保护状态
        privacyProtection.checkProtectionStatus(floatingWindow!).then(status => {
          console.log(' Floating window protection status:', status ? 'Active' : 'Partial')
        })
      }, 150) // 稍长的延迟，确保更隐蔽
    }
  })

  // 生产环境禁止浮动窗口DevTools
  disableDevTools(floatingWindow!)

  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    floatingWindow.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/#/floating')
  } else {
    floatingWindow.loadFile(join(__dirname, '../renderer/index.html'), { hash: '/floating' })
  }

  floatingWindow.on('closed', () => {
    floatingWindow = null
  })

  return floatingWindow
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(async () => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.geekassistant.app')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  // 初始化服务管理器
  try {
    console.log(' Initializing service manager...')

    const success = await serviceManager.initialize()
    if (success) {
      console.log(' Service manager initialized successfully')



      // 设置AI回复事件监听器（修复AI回复显示问题）
      serviceManager.on('ai-response-received', (response: any) => {
        console.log(' Main process received AI response from ServiceManager:', response)
        if (response && response.text) {
          console.log(' Forwarding AI response to renderer:', response.text)
          sendToRenderer('ai-response', response.text)
        }
      })

      // 🎯 方案A：只显示partial转录，不发送final转录到renderer
      // 注释掉main process的转录发送，避免与SeparatedAdapter的partial显示冲突
      serviceManager.on('transcription-received', (transcription: any) => {
        console.log(' Main process received transcription from ServiceManager:', transcription)
        // 🚫 不再发送final转录到renderer，只由SeparatedAdapter发送partial转录
        // if (transcription && transcription.text) {
        //   sendToRenderer('transcription-update', transcription.text)
        // }
      })

    } else {
      console.error(' Failed to initialize service manager')
    }
  } catch (error) {
    console.error(' Error initializing service manager:', error)
  }

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  stopSystemAudioCapture()
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', async () => {
  stopSystemAudioCapture()

  // 清理音频流管理器
  if (audioStreamManager) {
    try {
      await audioStreamManager.stop()
      audioStreamManager = null
      console.log(' Audio stream manager cleaned up')
    } catch (error) {
      console.error(' Error cleaning up audio stream manager:', error)
    }
  }

  // 清理服务管理器
  try {
    await serviceManager.cleanup()
    console.log('Service manager cleaned up')
  } catch (error) {
    console.error('Error cleaning up service manager:', error)
  }
})

// IPC handlers
ipcMain.handle('enter-collaboration-mode', () => {
  try {
    console.log('Entering collaboration mode...')
    if (mainWindow) {
      // 窗口大小已统一，无需调整
      console.log('Collaboration mode activated')
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to enter collaboration mode:', error)
    return false
  }
})

// 测试音频数据流程的IPC处理器
ipcMain.handle('test-audio-flow', async () => {
  console.log(' Testing audio flow without SystemAudioDump...')
  return await testAudioDataFlow()
})

ipcMain.handle('exit-collaboration-mode', () => {
  try {
    console.log('Exiting collaboration mode...')
    if (mainWindow) {
      // 窗口大小已统一，无需调整
      console.log('Collaboration mode deactivated')
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to exit collaboration mode:', error)
    return false
  }
})

// 保留原有的浮窗功能作为备用
ipcMain.handle('create-floating-window', () => {
  try {
    console.log('Creating floating window...')
    if (!floatingWindow) {
      const window = createFloatingWindow()
      console.log('Floating window created successfully:', !!window)
      return true
    } else {
      console.log('Floating window already exists')
      floatingWindow.show()
      floatingWindow.focus()
      return true
    }
  } catch (error) {
    console.error('Failed to create floating window:', error)
    return false
  }
})

ipcMain.handle('close-floating-window', () => {
  if (floatingWindow) {
    floatingWindow.close()
    floatingWindow = null
  }
  return true
})

ipcMain.handle('initialize-gemini', async (_event, apiKey: string, customPrompt = '', profile = 'interview', language = 'cmn-CN') => {
  console.log(' IPC: initialize-gemini called with:', {
    apiKeyLength: apiKey?.length || 0,
    profile,
    language,
    customPromptLength: customPrompt?.length || 0
  })

  // 优先使用原始方法，确保稳定性
  console.log(' Using original Gemini initialization method for stability')
  return await initializeGeminiSession(apiKey, customPrompt, profile, language)
})

ipcMain.handle('start-audio-capture', async () => {
  console.log(' IPC: start-audio-capture called')

  try {
    // 🎯 首先根据当前转录服务配置音频参数
    const currentConfig = serviceManager.getCurrentConfig()
    const transcriptionProvider = currentConfig?.separated?.transcription?.provider
    if (transcriptionProvider) {
      updateAudioConfigForTranscriptionService(transcriptionProvider)
    }

    // 首先启动服务会话
    console.log(' Starting service session...')
    const sessionStarted = await serviceManager.startSession()
    console.log(' Service session result:', sessionStarted)

    // 然后启动音频捕获
    console.log(' Starting system audio capture...')
    const audioStarted = await startSystemAudioCapture()
    console.log(' Audio capture result:', audioStarted)

    return audioStarted && sessionStarted
  } catch (error) {
    console.error(' Error starting audio capture and session:', error)
    return false
  }
})

ipcMain.handle('restart-audio-capture', async () => {
  console.log(' IPC: restart-audio-capture called - forcing restart')
  
  // 强制重置状态
  isAudioCaptureActive = false
  if (systemAudioProc) {
    try {
      systemAudioProc.kill('SIGTERM')
    } catch (error) {
      console.log(' Error killing existing process during restart:', error)
    }
    systemAudioProc = null
  }
  
  // 清理所有SystemAudioDump进程
  await killExistingSystemAudioDump()
  
  // 重新启动服务会话和音频捕获
  try {
    const sessionStarted = await serviceManager.startSession()
    console.log(' Service session restart result:', sessionStarted)

    const audioStarted = await startSystemAudioCapture()
    console.log(' Audio capture restart result:', audioStarted)

    return audioStarted && sessionStarted
  } catch (error) {
    console.error(' Error restarting session and audio:', error)
    return false
  }
})

ipcMain.handle('stop-audio-capture', async () => {
  // 使用服务管理器停止会话，同时停止音频捕获
  try {
    await serviceManager.stopSession()
    stopSystemAudioCapture()
    return true
  } catch (error) {
    console.error('Error stopping session via service manager:', error)
    stopSystemAudioCapture()
    return true
  }
})

ipcMain.handle('reconnect-gemini', async () => {
  // 使用服务管理器重连，保持向后兼容
  try {
    const success = await serviceManager.reconnect()
    if (success) {
      return true
    }
  } catch (error) {
    console.error('Error reconnecting via service manager:', error)
  }

  // 降级到原始方法
  if (currentApiKey) {
    reconnectAttempts = 0 // 重置重连计数
    return await initializeGeminiSession(currentApiKey, currentCustomPrompt, currentProfile, currentLanguage)
  }
  return false
})

ipcMain.handle('disconnect-gemini', async () => {
  console.log('Manual disconnect requested')

  // 使用服务管理器断开连接
  try {
    await serviceManager.disconnect()
    stopSystemAudioCapture() // 停止音频捕获
    return true
  } catch (error) {
    console.error('Error disconnecting via service manager:', error)
  }

  // 降级到原始方法
  if (geminiSession) {
    geminiSession.close()
    geminiSession = null
  }
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
    reconnectTimeout = null
  }
  reconnectAttempts = maxReconnectAttempts // 防止自动重连
  stopSystemAudioCapture() // 停止音频捕获
  sendToRenderer('session-closed')
  return true
})

ipcMain.handle('manual-reconnect', async () => {
  console.log('Manual reconnect requested')

  // 使用服务管理器重连
  try {
    const success = await serviceManager.reconnect()
    if (success) {
      sendToRenderer('session-paused-silence', false)
      sendToRenderer('update-status', 'Manual reconnect successful')
      return true
    }
  } catch (error) {
    console.error('Error with manual reconnect via service manager:', error)
  }

  // 降级到原始方法
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
    reconnectTimeout = null
  }
  reconnectAttempts = 0 // 重置重连计数

  if (currentApiKey) {
    const success = await initializeGeminiSession(currentApiKey, currentCustomPrompt, currentProfile, currentLanguage)
    if (success) {
      sendToRenderer('session-paused-silence', false)
      sendToRenderer('update-status', 'Manual reconnect successful')
    }
    return success
  }
  return false
})

// 新增服务管理相关的IPC处理器
ipcMain.handle('get-service-status', () => {
  try {
    return serviceManager.getServiceStatus()
  } catch (error) {
    console.error('Error getting service status:', error)
    return null
  }
})

ipcMain.handle('get-current-service-config', () => {
  try {
    return serviceManager.getCurrentConfig()
  } catch (error) {
    console.error('Error getting current service config:', error)
    return null
  }
})

// 重置服务配置为默认值
ipcMain.handle('reset-service-config', async () => {
  try {
    const { serviceConfigManager } = await import('./services/config/ServiceConfig')
    await serviceConfigManager.resetToDefault()

    // 重新初始化服务管理器
    const success = await serviceManager.initialize()
    return success
  } catch (error) {
    console.error('Error resetting service config:', error)
    return false
  }
})

// 防止重复的服务切换请求 - 使用Map来跟踪不同配置的切换
const switchingServices = new Map<string, { inProgress: boolean, timestamp: number }>()

ipcMain.handle('switch-service', async (_event, config) => {
  const configKey = JSON.stringify(config)
  const now = Date.now()
  const SWITCH_TIMEOUT = 30000 // 30秒超时

  // 检查是否有正在进行的切换，并清理超时的锁
  const existing = switchingServices.get(configKey)
  if (existing) {
    if (existing.inProgress && (now - existing.timestamp) < SWITCH_TIMEOUT) {
      console.log('Service switch already in progress for this configuration, ignoring duplicate request')
      return { success: false, error: 'Service switch already in progress for this configuration' }
    } else if (existing.inProgress && (now - existing.timestamp) >= SWITCH_TIMEOUT) {
      console.log('Service switch timeout detected, clearing stale lock')
      switchingServices.delete(configKey)
    }
  }

  switchingServices.set(configKey, { inProgress: true, timestamp: now })

  try {
    console.log('Switching service via IPC:', config)
    const success = await serviceManager.switchToService(config)

    // 🐾 修复：服务切换后重新初始化ImprovedStreamManager
    if (success && improvedStreamManager) {
      try {
        console.log('🐾 Reinitializing ImprovedStreamManager after service switch...')

        // 停止当前的ImprovedStreamManager
        await improvedStreamManager.stop()
        improvedStreamManager = null
        isStreamManagerActive = false

        // 重新初始化ImprovedStreamManager以使用新的服务配置
        const reinitResult = await initializeImprovedStreamManager()
        if (reinitResult) {
          console.log('🐾✅ ImprovedStreamManager reinitialized successfully with new service config')
        } else {
          console.warn('🐾⚠️ Failed to reinitialize ImprovedStreamManager')
        }
      } catch (streamError) {
        console.error('🐾❌ Error reinitializing ImprovedStreamManager:', streamError)
        // 不让StreamManager的错误影响服务切换的结果
      }
    }

    return { success, error: null }
  } catch (error) {
    console.error('Error switching service:', error)
    return { success: false, error: (error as Error).message }
  } finally {
    switchingServices.set(configKey, { inProgress: false, timestamp: now })
    
    // 定期清理过期的锁（防止内存泄漏）
    if (switchingServices.size > 10) {
      const cutoffTime = now - SWITCH_TIMEOUT
      for (const [key, value] of switchingServices.entries()) {
        if (!value.inProgress && value.timestamp < cutoffTime) {
          switchingServices.delete(key)
        }
      }
    }
  }
})

ipcMain.handle('update-service-config', async (_event, config) => {
  try {
    console.log('🔧 Updating service config via IPC:', config)

    let success = false

    // 🔧 修复：统一使用ServiceConfigManager保存配置
    const { serviceConfigManager } = await import('./services/config/ServiceConfig')

    // 先保存配置到ServiceConfigManager
    try {
      await serviceConfigManager.updateConfig(config)
      console.log('🔧 Configuration saved to ServiceConfigManager successfully')
      success = true
    } catch (error) {
      console.error('🔧❌ Failed to save config to ServiceConfigManager:', error)
      success = false
    }

    // 如果配置保存成功，再更新运行时服务
    if (success) {
      console.log('🔧 Updating ServiceManager with new config')
      await serviceManager.updateConfig(config)
      console.log('🔧 ServiceManager updated successfully')

      // 🔧 修复：通知前端配置已更新
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('service-config-updated', config)
        console.log('🔧 Notified frontend about config update')
      }
    }

    return success
  } catch (error) {
    console.error('🔧❌ Error updating service config:', error)
    return false
  }
})

// 权限相关的IPC处理器
ipcMain.handle('check-permissions', async () => {
  return await getAllPermissionsStatus()
})

ipcMain.handle('check-screen-recording-permission', async () => {
  return await checkScreenRecordingPermission()
})

ipcMain.handle('check-microphone-permission', async () => {
  return await checkMicrophonePermission()
})

ipcMain.handle('check-api-key-status', async () => {
  return await checkApiKeyStatus()
})

ipcMain.handle('check-audio-device-status', async () => {
  return await checkAudioDeviceStatus()
})

ipcMain.handle('open-system-preferences', async (_event, pane: string) => {
  return await openSystemPreferences(pane)
})

ipcMain.handle('test-audio-capture', async () => {
  return await testAudioCapture()
})

ipcMain.handle('get-current-audio-config', () => {
  return {
    ...currentAudioConfig,
    provider: serviceManager.getCurrentConfig()?.separated?.transcription?.provider || 'unknown'
  }
})

// 🎨 自定义API管理相关的IPC处理器
ipcMain.handle('custom-api:get-all', async () => {
  try {
    await customAPIManager.initialize()
    return customAPIManager.getAllAPIs()
  } catch (error) {
    console.error('🎨❌ Error getting custom APIs:', error)
    return []
  }
})

ipcMain.handle('custom-api:add', async (_event, config) => {
  try {
    await customAPIManager.initialize()
    const id = await customAPIManager.addAPI(config)
    console.log('🎨✅ Custom API added:', id)
    return id
  } catch (error) {
    console.error('🎨❌ Error adding custom API:', error)
    return null
  }
})

ipcMain.handle('custom-api:update', async (_event, id, updates) => {
  try {
    const success = await customAPIManager.updateAPI(id, updates)
    console.log('🎨✅ Custom API updated:', id, success)
    return success
  } catch (error) {
    console.error('🎨❌ Error updating custom API:', error)
    return false
  }
})

ipcMain.handle('custom-api:remove', async (_event, id) => {
  try {
    const success = await customAPIManager.removeAPI(id)
    console.log('🎨✅ Custom API removed:', id, success)
    return success
  } catch (error) {
    console.error('🎨❌ Error removing custom API:', error)
    return false
  }
})

ipcMain.handle('custom-api:import-url', async (_event, url) => {
  try {
    await customAPIManager.initialize()
    const id = await customAPIManager.importFromURL(url)
    console.log('🎨✅ Custom API imported from URL:', id)
    return id
  } catch (error) {
    console.error('🎨❌ Error importing custom API from URL:', error)
    return null
  }
})

ipcMain.handle('custom-api:export-url', async (_event, id) => {
  try {
    const url = customAPIManager.exportToURL(id)
    console.log('🎨✅ Custom API exported to URL:', url)
    return url
  } catch (error) {
    console.error('🎨❌ Error exporting custom API to URL:', error)
    return null
  }
})

ipcMain.handle('custom-api:test', async (_event, id) => {
  try {
    const result = await customAPIManager.testAPI(id)
    console.log('🎨✅ Custom API test result:', result)
    return result
  } catch (error) {
    console.error('🎨❌ Error testing custom API:', error)
    return { success: false, message: '测试失败' }
  }
})

ipcMain.handle('custom-api:test-chat', async (_event, id, question) => {
  try {
    const result = await customAPIManager.testAPIChat(id, question)
    console.log('🎨✅ Custom API chat test result:', result)
    return result
  } catch (error) {
    console.error('🎨❌ Error testing custom API chat:', error)
    return { success: false, message: '问答测试失败' }
  }
})

ipcMain.handle('request-microphone-permission', async () => {
  try {
    const granted = await systemPreferences.askForMediaAccess('microphone')
    return {
      granted,
      message: granted ? '麦克风权限已授予' : '麦克风权限被拒绝'
    }
  } catch (error) {
    return {
      granted: false,
      message: `请求麦克风权限失败: ${(error as Error).message}`
    }
  }
})

async function initializeGeminiSession(apiKey: string, customPrompt = '', profile = 'interview', language = 'cmn-CN'): Promise<boolean> {
  if (isInitializingSession) {
    console.log('Session initialization already in progress')
    return false
  }

  console.log('Initializing Gemini session with:', {
    apiKeyLength: apiKey?.length || 0,
    profile,
    language,
    customPromptLength: customPrompt?.length || 0
  })

  // 验证API密钥
  if (!apiKey || apiKey.trim() === '') {
    console.error('Invalid API key provided')
    sendToRenderer('session-error', 'API密钥无效或为空')
    return false
  }

  // 保存当前配置用于重连
  currentApiKey = apiKey
  currentCustomPrompt = customPrompt
  currentProfile = profile
  currentLanguage = language

  isInitializingSession = true
  sendToRenderer('session-initializing', true)

  // 清除之前的重连定时器
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
    reconnectTimeout = null
  }

  try {
    console.log('Creating GoogleGenAI client...')
    const client = new GoogleGenAI({
      apiKey: apiKey,
    })

    const systemPrompt = getSystemPrompt(profile, customPrompt, false)
    console.log('System prompt generated, length:', systemPrompt.length)
    console.log('Connecting to Gemini Live API...')
    const session = await client.live.connect({
      model: 'gemini-live-2.5-flash-preview',
      callbacks: {
        onopen: function () {
          console.log('Gemini session opened successfully')
          reconnectAttempts = 0 // 重置重连计数
          sendToRenderer('update-status', 'Connected to Gemini - Starting recording...')
        },
        onmessage: function (message: any) {
          console.log('----------------', message)

          // Handle transcription input - 完全按照 cheatingdaddy 的方式
          if (message.serverContent?.inputTranscription?.text) {
            currentTranscription += message.serverContent.inputTranscription.text
            // 立即发送转录片段到前端，就像 cheatingdaddy 一样
            sendToRenderer('transcription-update', currentTranscription)
          }

          // Handle AI model response - 完全按照 cheatingdaddy 的方式
          if (message.serverContent?.modelTurn?.parts) {
            for (const part of message.serverContent.modelTurn.parts) {
              console.log('AI Part:', part)
              if (part.text) {
                messageBuffer += part.text
              }
            }
          }

          // 当生成完成时发送完整的 AI 回复 - 完全按照 cheatingdaddy 的方式
          if (message.serverContent?.generationComplete) {
            console.log('Generation Complete - AI Response:', messageBuffer)
            if (messageBuffer.trim()) {
              console.log(' Sending AI response to frontend:', messageBuffer)
              sendToRenderer('ai-response', messageBuffer)

              // 保存对话记录
              if (currentTranscription && messageBuffer) {
                console.log('Saving conversation turn:', { transcription: currentTranscription, response: messageBuffer })
                currentTranscription = '' // 重置转录
              }
            }
            messageBuffer = '' // 重置消息缓冲区
          }

          // 处理对话轮次完成
          if (message.serverContent?.turnComplete) {
            sendToRenderer('update-status', 'Listening...')
          }
        },
        onerror: function (error: any) {
          console.error('Gemini session error:', error)
          const errorMessage = error instanceof Error ? error.message : String(error)

          // 简化错误处理，直接报告错误
          sendToRenderer('session-error', `Gemini API 连接错误: ${errorMessage}`)

          // 检查是否是认证错误，如果是则停止重连
          if (errorMessage.includes('EIO') || errorMessage.includes('API key') || errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
            console.log('Authentication error detected - stopping reconnection attempts')
            currentApiKey = null
            reconnectAttempts = maxReconnectAttempts
            return
          }

          // 其他错误尝试重连
          if (reconnectAttempts < maxReconnectAttempts) {
            scheduleReconnect()
          }
        },
        onclose: function (e) {
          console.log('Gemini session closed:', e?.reason || 'Unknown reason')
          geminiSession = null
          sendToRenderer('session-closed')

          const reason = e?.reason || ''

          // 检查是否是配置错误（语言、认证等）
          if (reason.includes('language') || reason.includes('API key') || reason.includes('authentication') || reason.includes('unauthorized')) {
            console.log('Session closed due to configuration error:', reason)
            currentApiKey = null
            reconnectAttempts = maxReconnectAttempts
            sendToRenderer('session-error', `配置错误: ${reason}`)
            return
          }

          // 其他情况尝试重连
          if (reconnectAttempts < maxReconnectAttempts && currentApiKey && !isInitializingSession) {
            console.log('Session closed unexpectedly, scheduling reconnect...')
            scheduleReconnect()
          } else {
            sendToRenderer('update-status', 'Session closed')
          }
        }
      },
      config: {
        responseModalities: ['text' as any],
        inputAudioTranscription: {}, // 启用音频转录
        contextWindowCompression: { slidingWindow: {} },
        speechConfig: { languageCode: currentLanguage },
        systemInstruction: {
          parts: [{ text: systemPrompt }],
        },
      },
    })

    geminiSession = session
    isInitializingSession = false
    sendToRenderer('session-initializing', false)
    console.log('Gemini session initialized successfully')
    return true
  } catch (error: unknown) {
    console.error('Failed to initialize Gemini session:', error)

    let errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage.includes('API_KEY_INVALID') || errorMessage.includes('401') || errorMessage.includes('API key')) {
      errorMessage = 'API密钥无效，请检查.env.local文件中的VITE_GEMINI_API_KEY配置'
      currentApiKey = null // 清除无效的API密钥
    } else if (errorMessage.includes('PERMISSION_DENIED') || errorMessage.includes('403')) {
      errorMessage = 'API权限被拒绝，请检查API密钥权限'
    } else if (errorMessage.includes('language') || errorMessage.includes('Language')) {
      errorMessage = '语言配置错误，已自动修复为支持的语言代码'
    } else if (errorMessage.includes('NETWORK') || errorMessage.includes('fetch')) {
      errorMessage = '网络连接错误，请检查网络连接'
    }

    console.error('Session initialization error:', errorMessage)
    isInitializingSession = false
    sendToRenderer('session-initializing', false)
    sendToRenderer('session-error', errorMessage)
    return false
  }
}

function sendToRenderer(channel: string, data?: any) {
  try {
    const windows = BrowserWindow.getAllWindows()
    if (windows.length === 0) {
      return
    }

    windows.forEach(window => {
      if (window && !window.isDestroyed()) {
        window.webContents.send(channel, data)
      }
    })
  } catch (error) {
    console.error(' Error sending message to renderer:', error)
  }
}

function scheduleReconnect() {
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout)
  }

  // 检查是否应该重连
  if (!currentApiKey || isInitializingSession) {
    console.log('Skipping reconnect: no API key or already initializing')
    return
  }

  reconnectAttempts++
  const delay = Math.min(1000 * Math.pow(2, reconnectAttempts - 1), 30000) // 指数退避，最大30秒

  console.log(`Scheduling reconnect attempt ${reconnectAttempts}/${maxReconnectAttempts} in ${delay}ms`)
  sendToRenderer('update-status', `Connection lost. Reconnecting in ${Math.ceil(delay / 1000)}s... (${reconnectAttempts}/${maxReconnectAttempts})`)

  reconnectTimeout = setTimeout(async () => {
    if (reconnectAttempts > maxReconnectAttempts) {
      console.log('Maximum reconnect attempts reached')
      sendToRenderer('session-error', 'Failed to reconnect after maximum attempts')
      return
    }

    console.log(`Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts})`)
    sendToRenderer('update-status', 'Reconnecting...')

    try {
      if (!currentApiKey) return
      const success = await initializeGeminiSession(currentApiKey, currentCustomPrompt, currentProfile, currentLanguage)
      if (!success) {
        console.log('Reconnect failed, will retry if attempts remaining')
        if (reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect()
        } else {
          sendToRenderer('session-error', 'Failed to reconnect after maximum attempts')
        }
      } else {
        console.log('Reconnect successful')
        reconnectAttempts = 0 // 重置重连计数
      }
    } catch (error: unknown) {
      console.error('Error during reconnect:', error)
      if (reconnectAttempts < maxReconnectAttempts) {
        scheduleReconnect()
      } else {
        sendToRenderer('session-error', 'Failed to reconnect after maximum attempts')
      }
    }
  }, delay)
}

async function startSystemAudioCapture(): Promise<boolean> {
  // 防止并发启动
  if (isStartingAudioCapture) {
    console.log(' Audio capture is already starting, skipping duplicate request')
    return false
  }

  // 检查是否已经在运行音频捕获
  if (isAudioCaptureActive && systemAudioProc && !systemAudioProc.killed) {
    console.log(' Audio capture is already active, skipping duplicate start')
    return true
  }

  try {
    isStartingAudioCapture = true

    // 🐾 修复：检查音频权限而不是屏幕录制权限
    const audioPermission = await checkMicrophonePermission()
    if (!audioPermission.granted) {
      console.log(' Audio permission not granted:', audioPermission.message)
      sendToRenderer('session-error', `音频捕获需要音频权限：${audioPermission.message}`)
      return false
    }

    if (process.platform === 'darwin') {
      console.log(' Starting macOS audio capture with proper permissions...')

      // 首先初始化改进的音频流管理器
      console.log(' Initializing improved stream manager for better audio processing...')
      const improvedStreamInitialized = await initializeImprovedStreamManager()
      if (!improvedStreamInitialized) {
        console.warn(' Improved stream manager initialization failed, falling back to original')
        // 降级到原有的音频流管理器
        const streamManagerInitialized = await initializeAudioStreamManager()
        if (!streamManagerInitialized) {
          console.warn(' Both stream managers failed to initialize, continuing with basic processing')
        }
      }

      const result = await startMacOSAudioCapture()
      if (result) {
        isAudioCaptureActive = true
        console.log(' Audio capture started successfully with improved stream manager')
      } else {
        console.warn(' Audio capture failed to start')
        // 🐾 修复：音频捕获失败时不要清理ImprovedStreamManager，保留它用于测试
        console.log(' 🐾 Keeping ImprovedStreamManager available for testing even though audio capture failed')
        sendToRenderer('update-status', '音频捕获失败，但转录和AI服务可用于测试')
        // 不清理ImprovedStreamManager，让它可以用于手动测试
      }
      // 🐾 修复：即使音频捕获失败，也返回true让应用继续运行
      return true
    } else {
      console.log('System audio capture not implemented for this platform')
      sendToRenderer('session-error', 'System audio capture not supported on this platform')
      return false
    }
  } finally {
    // 确保重置启动标志
    isStartingAudioCapture = false
  }
}

async function killExistingSystemAudioDump(): Promise<void> {
  return new Promise((resolve) => {
    console.log('Checking for existing SystemAudioDump processes...')

    // 首先尝试优雅地终止现有进程
    if (systemAudioProc && !systemAudioProc.killed) {
      console.log(' Stopping existing SystemAudioDump process gracefully...')
      systemAudioProc.kill('SIGTERM')
      systemAudioProc = null
      isAudioCaptureActive = false
    }

    // Kill any existing SystemAudioDump processes
    const killProc = spawn('pkill', ['-f', 'SystemAudioDump'], {
      stdio: 'ignore',
    })

    killProc.on('close', (code) => {
      if (code === 0) {
        console.log('Killed existing SystemAudioDump processes')
      } else {
        console.log('No existing SystemAudioDump processes found')
      }
      // 等待一小段时间确保进程完全清理
      setTimeout(resolve, 500)
    })

    killProc.on('error', (err) => {
      console.log('Error killing SystemAudioDump processes:', err)
      setTimeout(resolve, 500)
    })

    // Timeout after 5 seconds
    setTimeout(() => {
      killProc.kill()
      setTimeout(resolve, 500)
    }, 5000)
  })
}

async function startMacOSAudioCapture(): Promise<boolean> {
  if (process.platform !== 'darwin') return false

  // 更严格的状态检查和清理
  if (isAudioCaptureActive && systemAudioProc && !systemAudioProc.killed) {
    console.log(' Audio capture already active with valid process, skipping')
    return true
  }

  // 强制重置状态和清理进程
  console.log(' Resetting audio capture state and cleaning up processes...')
  isAudioCaptureActive = false
  if (systemAudioProc) {
    try {
      systemAudioProc.kill('SIGTERM')
    } catch (error) {
      console.log(' Error killing existing process:', error)
    }
    systemAudioProc = null
  }

  // Kill any existing SystemAudioDump processes first
  await killExistingSystemAudioDump()

  console.log(' Starting macOS audio capture with SystemAudioDump...')

  // 🐾 修复：检查CPU架构兼容性
  const { execSync } = require('child_process')
  try {
    const systemArch = execSync('uname -m').toString().trim()
    console.log(' System architecture:', systemArch)

    // 检查SystemAudioDump架构
    const systemAudioPath = app.isPackaged
      ? join(process.resourcesPath, 'SystemAudioDump')
      : join(__dirname, '../../assets/SystemAudioDump')

    if (require('fs').existsSync(systemAudioPath)) {
      const fileInfo = execSync(`file "${systemAudioPath}"`).toString()
      console.log(' SystemAudioDump info:', fileInfo)

      // 🐾 修复：正确检测Universal Binary
      if (systemArch === 'x86_64') {
        if (fileInfo.includes('arm64') && !fileInfo.includes('x86_64')) {
          // 只有ARM64，没有x86_64
          console.error(' ❌ Architecture mismatch: Intel Mac cannot run ARM64-only SystemAudioDump')
          console.log(' 🐾 Need Intel version of SystemAudioDump for system audio capture')

          sendToRenderer('session-error',
            '检测到Intel Mac，但SystemAudioDump只支持ARM64。\n\n' +
            '解决方案：\n' +
            '1. 获取Intel版本的SystemAudioDump\n' +
            '2. 或安装BlackHole虚拟音频设备\n' +
            '3. 当前只能使用其他音频输入方式\n\n' +
            '应用其他功能正常可用。'
          )

          return false
        } else if (fileInfo.includes('x86_64')) {
          // 包含x86_64架构，可以运行
          console.log(' ✅ SystemAudioDump supports x86_64 architecture, proceeding...')
        }
      }
    }
  } catch (error) {
    console.warn(' Architecture check failed:', error)
  }

  let systemAudioPath: string
  if (app.isPackaged) {
    systemAudioPath = join(process.resourcesPath, 'SystemAudioDump')
  } else {
    // 在开发环境中，我们需要提供 SystemAudioDump 的路径
    systemAudioPath = join(__dirname, '../../assets/SystemAudioDump')
  }

  console.log(' SystemAudioDump path:', systemAudioPath)

  // 检查文件是否存在
  const fs = require('fs')
  if (!fs.existsSync(systemAudioPath)) {
    console.error(' SystemAudioDump file not found at:', systemAudioPath)
    return false
  }
  console.log(' SystemAudioDump file exists')

  try {
    // 添加重试逻辑，最多尝试3次启动SystemAudioDump
    let retryCount = 0
    const maxRetries = 3

    while (retryCount < maxRetries) {
      try {
        console.log(` Spawning SystemAudioDump process (attempt ${retryCount + 1}/${maxRetries})...`)

        // 每次重试前清理
        if (systemAudioProc) {
          try {
            systemAudioProc.kill('SIGKILL')
          } catch (e) {
            // 忽略清理错误
          }
          systemAudioProc = null
        }

        // 等待一小段时间让系统清理
        if (retryCount > 0) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }

        systemAudioProc = spawn(systemAudioPath, [], {
          stdio: ['ignore', 'pipe', 'pipe'],
        })

        if (!systemAudioProc.pid) {
          throw new Error('Failed to get PID')
        }

        console.log(' SystemAudioDump started with PID:', systemAudioProc.pid)
        isAudioCaptureActive = true
        break // 成功启动，跳出重试循环

      } catch (spawnError) {
        console.error(` SystemAudioDump spawn attempt ${retryCount + 1} failed:`, spawnError)
        retryCount++

        if (retryCount >= maxRetries) {
          console.error(' All SystemAudioDump spawn attempts failed')
          isAudioCaptureActive = false
          return false
        }
      }
    }

    // 🚨 紧急修复：SystemAudioDump固定输出24kHz，我们需要正确处理
    const CHUNK_DURATION = 0.5 // 500ms chunks - 减少切分频率
    const SYSTEM_AUDIO_SAMPLE_RATE = 24000  // SystemAudioDump固定输出24kHz
    const TARGET_SAMPLE_RATE = currentAudioConfig.sampleRate  // 目标采样率
    const BYTES_PER_SAMPLE = 2
    const CHANNELS = 2 // SystemAudioDump 输出立体声，后续转换为单声道
    const CHUNK_SIZE = SYSTEM_AUDIO_SAMPLE_RATE * BYTES_PER_SAMPLE * CHANNELS * CHUNK_DURATION

    console.log(`🚨 SystemAudioDump: ${SYSTEM_AUDIO_SAMPLE_RATE}Hz → Target: ${TARGET_SAMPLE_RATE}Hz`);

    let audioBuffer = Buffer.alloc(0)
    const maxBufferSize = CHUNK_SIZE * 50 // 5秒缓冲

    systemAudioProc?.stdout?.on('data', async (data: Buffer) => {
      audioChunkCount++
      // 极致优化：大幅减少日志输出
      if (audioChunkCount % 1000 === 0) {
        console.log(`🎵 音频流正常 (${audioChunkCount}块)`)
      }
      // 第一次接收到音频数据时打印日志
      if (audioChunkCount === 1) {
        console.log(' 首次接收到音频数据，数据长度:', data.length)
        console.log(' 音频捕获工作正常，开始处理音频流...')
      }
      audioBuffer = Buffer.concat([audioBuffer, data])

      // 处理完整的音频块
      while (audioBuffer.length >= CHUNK_SIZE) {
        const chunk = audioBuffer.slice(0, CHUNK_SIZE)
        audioBuffer = audioBuffer.slice(CHUNK_SIZE)

        // 转换立体声到单声道
        const monoChunk = stereoToMono(chunk)

        // 🚨 重采样到目标采样率
        const resampledChunk = resampleAudio(monoChunk, SYSTEM_AUDIO_SAMPLE_RATE, TARGET_SAMPLE_RATE)

        console.log(`🔄 Resampled: ${monoChunk.length}→${resampledChunk.length} bytes (${SYSTEM_AUDIO_SAMPLE_RATE}→${TARGET_SAMPLE_RATE}Hz)`)

        // 🎯 优化音频处理：直接发送Buffer，避免base64转换损失
        if (resampledChunk.length > 0) {
          try {
            const currentConfig = serviceManager.getCurrentConfig()
            if (currentConfig && currentConfig.mode === 'separated') {
              // 🎯 分离式服务：直接发送Buffer，避免数据转换损失
              const currentAdapter = serviceManager.getCurrentAdapter()
              if (currentAdapter && currentAdapter.sendAudio) {
                // Cast to SeparatedAdapter since we know it supports Buffer input in separated mode
                await (currentAdapter as any).sendAudio(resampledChunk)  // 直接发送Buffer
                console.log('🎯 Direct Buffer sent to SeparatedAdapter, size:', resampledChunk.length)
              }
            } else {
              // Gemini Live：仍使用base64（API要求）
              const audioData = resampledChunk.toString('base64')
              if (geminiSession) {
                sendAudioToGemini(audioData)
              }
            }
          } catch (error) {
            console.error(' Error in optimized audio processing:', error)
          }
        }

        // 定期保存调试音频（开发模式）
        if (process.env.NODE_ENV === 'development' && Math.random() < 0.05) {
          saveDebugAudio(resampledChunk, 'live_capture_resampled')
        }
      }

      // 限制缓冲区大小
      if (audioBuffer.length > maxBufferSize) {
        audioBuffer = audioBuffer.slice(-maxBufferSize)
      }
    })

    systemAudioProc?.stderr?.on('data', (data: Buffer) => {
      try {
        const errorMsg = data.toString()
        // 改进错误处理，特别关注流错误
        if (errorMsg.includes('Stream stopped with error') || errorMsg.includes('应用程序连接中断')) {
          console.error(' SystemAudioDump stream error detected:', errorMsg)

          // 立即尝试重启，不等待
          console.log(' Immediately restarting SystemAudioDump after stream error...')

          // 清理当前进程
          if (systemAudioProc && !systemAudioProc.killed) {
            try {
              systemAudioProc.kill('SIGKILL') // 使用SIGKILL强制终止
            } catch (e) {
              console.log(' Error killing process:', e)
            }
          }
          systemAudioProc = null
          isAudioCaptureActive = false

          // 立即重启
          setTimeout(async () => {
            console.log(' Attempting immediate restart after stream error...')
            try {
              await killExistingSystemAudioDump()
              const restartResult = await startMacOSAudioCapture()
              if (restartResult) {
                console.log(' SystemAudioDump restarted successfully after stream error')
                sendToRenderer('update-status', 'SystemAudioDump已重启，音频捕获恢复')
              } else {
                console.log(' Failed to restart SystemAudioDump after stream error')
                sendToRenderer('session-error', 'SystemAudioDump重启失败，请手动重启应用')
              }
            } catch (error) {
              console.error(' Error during SystemAudioDump restart:', error)
            }
          }, 1000) // 1秒后重启，更快响应

        } else if (errorMsg.includes('SWIFT TASK CONTINUATION MISUSE')) {
          // Swift continuation warning (non-critical)
        } else if (!errorMsg.includes('Capturing system audio') && !errorMsg.includes('Press ⌃C to stop')) {
          console.error('SystemAudioDump stderr:', errorMsg)
        }
      } catch (error) {
        // 忽略stderr处理错误，避免EIO问题
      }
    })

    systemAudioProc?.on('close', (code) => {
      console.log(' SystemAudioDump process closed with code:', code)
      systemAudioProc = null
      isAudioCaptureActive = false // 确保状态重置
      
      // 改进进程退出处理 - 所有非正常退出都视为错误
      if (code !== 0) {
        console.error(' SystemAudioDump异常退出，可能是权限或系统兼容性问题')
        sendToRenderer('session-error', 'SystemAudioDump工具异常退出，请检查屏幕录制权限或尝试重启应用')
        
        // 尝试自动重启音频捕获（最多重试一次）
        if (!systemAudioProc && audioChunkCount === 0) {
          console.log(' 尝试自动重启音频捕获...')
          setTimeout(async () => {
            try {
              await killExistingSystemAudioDump()
              const restartResult = await startSystemAudioCapture()
              if (restartResult) {
                console.log(' 音频捕获自动重启成功')
                sendToRenderer('update-status', '音频捕获已自动重启')
              } else {
                console.log(' 音频捕获自动重启失败')
              }
            } catch (error) {
              console.error(' 自动重启音频捕获时出错:', error)
            }
          }, 2000)
        }
      }
    })

    systemAudioProc?.on('error', (err) => {
      console.error(' SystemAudioDump process error:', err)
      systemAudioProc = null
      isAudioCaptureActive = false // 确保状态重置
      sendToRenderer('session-error', '音频捕获进程错误，请重试')
    })
  } catch (error) {
    console.error('Failed to start macOS audio capture:', error)
    return false
  }

  // 等待一小段时间确保进程启动
  await new Promise(resolve => setTimeout(resolve, 1000))

  return true
}

// 将立体声转换为单声道
function stereoToMono(stereoBuffer: Buffer): Buffer {
  const monoBuffer = Buffer.alloc(stereoBuffer.length / 2)

  for (let i = 0; i < stereoBuffer.length; i += 4) {
    // 读取左右声道的16位样本
    const left = stereoBuffer.readInt16LE(i)
    const right = stereoBuffer.readInt16LE(i + 2)

    // 平均值转换为单声道
    const mono = Math.round((left + right) / 2)

    // 写入单声道缓冲区
    monoBuffer.writeInt16LE(mono, i / 2)
  }

  return monoBuffer
}

// 🚨 音频重采样函数 - 从24kHz转换到目标采样率
function resampleAudio(audioBuffer: Buffer, fromRate: number, toRate: number): Buffer {
  if (fromRate === toRate) {
    return audioBuffer // 无需重采样
  }

  const ratio = fromRate / toRate
  const inputSamples = audioBuffer.length / 2 // 16位 = 2字节
  const outputSamples = Math.floor(inputSamples / ratio)
  const outputBuffer = Buffer.alloc(outputSamples * 2)

  for (let i = 0; i < outputSamples; i++) {
    const sourceIndex = Math.floor(i * ratio) * 2
    if (sourceIndex < audioBuffer.length - 1) {
      const sample = audioBuffer.readInt16LE(sourceIndex)
      outputBuffer.writeInt16LE(sample, i * 2)
    }
  }

  return outputBuffer
}

function stopSystemAudioCapture() {
  if (systemAudioProc) {
    console.log('Stopping SystemAudioDump...')
    systemAudioProc.kill('SIGTERM')
    systemAudioProc = null
    isAudioCaptureActive = false
  }
}

// 简化的音频处理 - 完全按照 cheatingdaddy 的方式

async function sendAudioToGemini(base64Data: string) {
  if (!geminiSession) {
    console.log(' No Gemini session available for audio')
    return
  }

  try {
    // 直接发送，不使用队列和批处理 - 完全按照 cheatingdaddy 的方式
    await geminiSession.sendRealtimeInput({
      audio: {
        data: base64Data,
        mimeType: `audio/pcm;rate=${currentAudioConfig.sampleRate}`,  // 🎯 动态采样率
      },
    })
    // 进一步减少音频发送日志的频率
    if (Math.random() < 0.001) { // 从1%减少到0.1%
      console.log(' Audio data sent to Gemini')
    }
  } catch (error) {
    console.error(' Error sending audio to Gemini:', error)
  }
}

// 🐾 测试音频数据流程函数
async function testAudioDataFlow(): Promise<boolean> {
  console.log('🐾 Starting audio data flow test...')

  try {
    // 检查当前服务模式
    const currentConfig = serviceManager.getCurrentConfig()
    const serviceMode = currentConfig?.mode || 'separated'
    console.log('🐾 Current service mode:', serviceMode)

    let streamMgr: ImprovedStreamManager | null = null
    if (serviceMode === 'separated') {
      streamMgr = improvedStreamManager // 之前已确保不为 null
    }

    if (!streamMgr) {
      console.error('🐾❌ ImprovedStreamManager not available for separated mode')
      sendToRenderer('session-error', 'ImprovedStreamManager未启动，无法测试分离式服务流程')
      return false
    }

    // 检查服务管理器是否可用
    const currentAdapter = serviceManager.getCurrentAdapter()
    if (!currentAdapter) {
      console.error('🐾❌ Service adapter not available')
      sendToRenderer('session-error', '服务适配器未启动，无法测试音频流程')
      return false
    }

    console.log('🐾✅ All components ready, generating test audio data...')
    sendToRenderer('update-status', `开始测试${serviceMode}模式的音频数据流程...`)

    // 🐾 修复：生成更长、更复杂的测试音频数据
    const sampleRate = 24000
    const duration = 5 // 增加到5秒
    const samples = sampleRate * duration
    const testAudioBuffer = Buffer.alloc(samples * 2) // 16位 = 2字节

    // 生成更复杂的测试音频（模拟语音频率范围）
    for (let i = 0; i < samples; i++) {
      // 混合多个频率，模拟语音特征
      const time = i / sampleRate
      const freq1 = 200 + 100 * Math.sin(2 * Math.PI * 2 * time) // 基频变化
      const freq2 = 800 + 200 * Math.sin(2 * Math.PI * 3 * time) // 共振峰
      const freq3 = 1500 + 300 * Math.sin(2 * Math.PI * 1.5 * time) // 高频成分

      const sample = (
        Math.sin(2 * Math.PI * freq1 * time) * 0.3 +
        Math.sin(2 * Math.PI * freq2 * time) * 0.2 +
        Math.sin(2 * Math.PI * freq3 * time) * 0.1
      ) * 16000 // 降低音量避免削波

      testAudioBuffer.writeInt16LE(Math.round(sample), i * 2)
    }

    console.log('🐾🎵 Generated test audio data:', testAudioBuffer.length, 'bytes')

    // 分块发送测试音频数据
    const chunkSize = 4800 // 100ms chunks at 24kHz
    let chunkNumber = 0

    for (let offset = 0; offset < testAudioBuffer.length; offset += chunkSize) {
      const chunk = testAudioBuffer.slice(offset, Math.min(offset + chunkSize, testAudioBuffer.length))
      chunkNumber++

      if (serviceMode === 'separated') {
        // Separated模式：发送到改进后的 StreamManager（已捕获非空引用）
        const success = streamMgr!.processAudioData(chunk, {
          timestamp: Date.now(),
          chunkNumber: chunkNumber,
          originalLength: chunk.length,
          processedLength: chunk.length
        })

        if (!success) {
          console.warn('🐾⚠️ Failed to process test audio chunk', chunkNumber)
        }
      } else if (serviceMode === 'gemini-live') {
        // Gemini Live模式：直接发送到Gemini
        const audioData = chunk.toString('base64')
        sendAudioToGemini(audioData)
        console.log('🐾🎵 Sent test audio chunk', chunkNumber, 'to Gemini Live')
      }

      // 小延迟模拟实时音频流
      await new Promise(resolve => setTimeout(resolve, 50))
    }

    console.log('🐾✅ Test audio data sent successfully to', serviceMode, 'mode,', chunkNumber, 'chunks')
    sendToRenderer('update-status', `测试音频数据已发送到${serviceMode}模式 (${chunkNumber} 块)`)

    // 🐾 添加调试：等待Gemini响应
    if (serviceMode === 'gemini-live') {
      console.log('🐾🔍 Waiting for Gemini Live response...')
      sendToRenderer('update-status', '等待Gemini Live响应...')

      // 设置超时检查
      setTimeout(() => {
        console.log('🐾⏰ 10秒后检查：如果没有看到Gemini响应，可能是音频格式或API配置问题')
        sendToRenderer('update-status', '如果没有响应，可能是音频格式问题')
      }, 10000)
    }

    return true

  } catch (error: unknown) {
    console.error('🐾❌ Error in test audio data flow:', error)
    sendToRenderer('session-error', `测试音频流程失败: ${error instanceof Error ? error.message : String(error)}`)
    return false
  }
}

// 权限检测函数
async function checkScreenRecordingPermission(): Promise<PermissionStatus> {
  try {
    // 检查屏幕录制权限
    const status = systemPreferences.getMediaAccessStatus('screen')
    console.log(' Current screen recording permission status:', status)

    if (status === 'granted') {
      return {
        granted: true,
        canRequest: false,
        message: '屏幕录制权限已授予'
      }
    } else if (status === 'denied') {
      return {
        granted: false,
        canRequest: false,
        message: '屏幕录制权限被拒绝，请在系统偏好设置 > 隐私与安全性 > 屏幕录制中手动开启Geek 助手应用'
      }
    } else if (status === 'not-determined') {
      // macOS 10.15+ does not allow apps to programmatically request screen recording permission.
      // We can only detect the status and instruct the user to grant it manually in System Settings.
      return {
        granted: false,
        canRequest: false,
        message:
          '首次使用需要授予屏幕录制权限，请在 系统设置 > 隐私与安全性 > 屏幕录制 中启用 Geek 助手'
      }
    } else {
      return {
        granted: false,
        canRequest: true,
        message: '需要屏幕录制权限以捕获系统音频'
      }
    }
  } catch (error) {
    console.error(' 检查屏幕录制权限时出错:', error)
    return {
      granted: false,
      canRequest: false,
      message: '无法检查屏幕录制权限状态，请手动检查系统偏好设置'
    }
  }
}

async function checkMicrophonePermission(): Promise<PermissionStatus> {
  try {
    const status = systemPreferences.getMediaAccessStatus('microphone')

    if (status === 'granted') {
      return {
        granted: true,
        canRequest: false,
        message: '麦克风权限已授予'
      }
    } else if (status === 'denied') {
      return {
        granted: false,
        canRequest: false,
        message: '麦克风权限被拒绝，请在系统偏好设置中手动授予'
      }
    } else {
      // 尝试请求权限
      const canRequest = await systemPreferences.askForMediaAccess('microphone')
      return {
        granted: canRequest,
        canRequest: !canRequest,
        message: canRequest ? '麦克风权限已授予' : '需要麦克风权限'
      }
    }
  } catch (error) {
    console.error('检查麦克风权限时出错:', error)
    return {
      granted: false,
      canRequest: false,
      message: '无法检查麦克风权限状态'
    }
  }
}

async function checkApiKeyStatus(): Promise<PermissionStatus> {
  try {
    // 从多个来源检查API密钥
    let apiKey = process.env.VITE_GEMINI_API_KEY || process.env.GEMINI_API_KEY

    // 如果环境变量中没有，尝试从localStorage或当前会话中获取
    if (!apiKey || apiKey.trim() === '') {
      // 检查当前会话是否有API密钥
      if (currentApiKey && currentApiKey.trim() !== '') {
        apiKey = currentApiKey
      }
    }

    // 如果仍然没有API密钥，但Gemini会话已经存在，说明API密钥是有效的
    if ((!apiKey || apiKey.trim() === '') && geminiSession) {
      return {
        granted: true,
        canRequest: false,
        message: 'API 密钥已通过Gemini会话验证'
      }
    }

    if (!apiKey || apiKey.trim() === '') {
      return {
        granted: false,
        canRequest: true,
        message: 'Gemini API 密钥未配置'
      }
    }

    if (apiKey.length < 30) {
      return {
        granted: false,
        canRequest: true,
        message: 'API 密钥格式可能不正确'
      }
    }

    return {
      granted: true,
      canRequest: false,
      message: 'API 密钥配置正确'
    }
  } catch (error) {
    console.error('检查API密钥时出错:', error)
    
    // 如果检查出错但Gemini会话存在，说明API密钥实际上是有效的
    if (geminiSession) {
      return {
        granted: true,
        canRequest: false,
        message: 'API 密钥已通过Gemini会话验证'
      }
    }
    
    return {
      granted: false,
      canRequest: true,
      message: '无法验证API密钥状态'
    }
  }
}

async function checkAudioDeviceStatus(): Promise<PermissionStatus> {
  try {
    // 首先检查屏幕录制权限，因为SystemAudioDump需要这个权限
    const screenRecordingStatus = systemPreferences.getMediaAccessStatus('screen')

    if (screenRecordingStatus !== 'granted') {
      return {
        granted: false,
        canRequest: true,
        message: 'SystemAudioDump 需要屏幕录制权限才能捕获系统音频'
      }
    }

    // 检查SystemAudioDump是否存在
    // const _systemAudioPath = app.isPackaged // 未使用，已注释
    //   ? join(process.resourcesPath, 'SystemAudioDump')
    //   : join(__dirname, '../../assets/SystemAudioDump')

    // 简化权限检查 - 如果屏幕录制权限已授予，就认为音频设备可用
    // 避免频繁的测试导致窗口闪烁
    return {
      granted: true,
      canRequest: false,
      message: '屏幕录制权限已授予，音频设备应该可以正常工作'
    }
  } catch (error) {
    console.error('检查音频设备时出错:', error)
    return {
      granted: false,
      canRequest: true,
      message: '无法检查音频设备状态'
    }
  }
}

async function getAllPermissionsStatus(): Promise<SystemPermissions> {
  try {
    const [screenRecording, microphone, apiKey, audioDevice] = await Promise.all([
      checkScreenRecordingPermission().catch(error => {
        console.error('Error checking screen recording permission:', error)
        return {
          granted: false,
          canRequest: true,
          message: '无法检查屏幕录制权限状态'
        }
      }),
      checkMicrophonePermission().catch(error => {
        console.error('Error checking microphone permission:', error)
        return {
          granted: false,
          canRequest: true,
          message: '无法检查麦克风权限状态'
        }
      }),
      checkApiKeyStatus().catch(error => {
        console.error('Error checking API key status:', error)
        return {
          granted: false,
          canRequest: true,
          message: '无法检查API密钥状态'
        }
      }),
      checkAudioDeviceStatus().catch(error => {
        console.error('Error checking audio device status:', error)
        return {
          granted: false,
          canRequest: true,
          message: '无法检查音频设备状态'
        }
      })
    ])

    return {
      screenRecording,
      microphone,
      apiKey,
      audioDevice
    }
  } catch (error) {
    console.error('Error in getAllPermissionsStatus:', error)
    // 返回默认的权限状态
    return {
      screenRecording: {
        granted: false,
        canRequest: true,
        message: '权限检查失败'
      },
      microphone: {
        granted: false,
        canRequest: true,
        message: '权限检查失败'
      },
      apiKey: {
        granted: false,
        canRequest: true,
        message: '权限检查失败'
      },
      audioDevice: {
        granted: false,
        canRequest: true,
        message: '权限检查失败'
      }
    }
  }
}

// 打开系统偏好设置
async function openSystemPreferences(pane: string): Promise<boolean> {
  try {
    let command: string

    switch (pane) {
      case 'screen-recording':
        command = 'open "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture"'
        break
      case 'microphone':
        command = 'open "x-apple.systempreferences:com.apple.preference.security?Privacy_Microphone"'
        break
      case 'privacy':
        command = 'open "x-apple.systempreferences:com.apple.preference.security"'
        break
      default:
        command = 'open "x-apple.systempreferences:com.apple.preference.security"'
    }

    await execAsync(command)
    return true
  } catch (error) {
    console.error('打开系统偏好设置失败:', error)
    return false
  }
}

// 测试音频捕获
async function testAudioCapture(): Promise<{ success: boolean; message: string; audioData?: number; silencePercentage?: number; recommendation?: string }> {
  try {
    const systemAudioPath = app.isPackaged
      ? join(process.resourcesPath, 'SystemAudioDump')
      : join(__dirname, '../../assets/SystemAudioDump')

    return new Promise((resolve) => {
      const testProc = spawn(systemAudioPath, [], {
        stdio: ['ignore', 'pipe', 'pipe']
      })

      let audioDataSize = 0
      let audioChunks: Buffer[] = []

      testProc.stdout?.on('data', (data: Buffer) => {
        audioDataSize += data.length
        audioChunks.push(data)
      })

      testProc.on('error', (error) => {
        resolve({
          success: false,
          message: `音频捕获测试失败: ${error.message}`,
          recommendation: '请检查SystemAudioDump是否正确安装'
        })
      })

      // 测试5秒以获得更准确的结果
      setTimeout(() => {
        testProc.kill('SIGTERM')

        if (audioDataSize === 0) {
          resolve({
            success: false,
            message: '音频捕获测试失败：没有捕获到任何音频数据',
            audioData: 0,
            silencePercentage: 100,
            recommendation: '请检查屏幕录制权限，并确保有音频正在播放'
          })
          return
        }

        // 分析音频质量
        const combinedBuffer = Buffer.concat(audioChunks)
        const audioStats = analyzeAudioBuffer(combinedBuffer, 'TestAudio')

        if (audioStats.silencePercentage >= 95) {
          resolve({
            success: false,
            message: `音频捕获到数据但全为静音 (${audioStats.silencePercentage.toFixed(1)}% 静音)`,
            audioData: audioDataSize,
            silencePercentage: audioStats.silencePercentage,
            recommendation: '请播放一些音频内容（音乐、视频等）然后重新测试'
          })
        } else if (audioStats.silencePercentage >= 80) {
          resolve({
            success: true,
            message: `音频捕获基本正常，但音量较低 (${audioStats.silencePercentage.toFixed(1)}% 静音)`,
            audioData: audioDataSize,
            silencePercentage: audioStats.silencePercentage,
            recommendation: '建议增加系统音量或播放更响亮的音频内容'
          })
        } else {
          resolve({
            success: true,
            message: `音频捕获正常！捕获了 ${audioDataSize} 字节数据 (${audioStats.silencePercentage.toFixed(1)}% 静音)`,
            audioData: audioDataSize,
            silencePercentage: audioStats.silencePercentage,
            recommendation: '音频捕获工作正常，可以使用Live Interview模式'
          })
        }
      }, 5000)
    })
  } catch (error) {
    return {
      success: false,
      message: `音频捕获测试出错: ${(error as Error).message}`,
      recommendation: '请检查SystemAudioDump是否正确安装和配置'
    }
  }
}

// Disable DevTools in production
function disableDevTools(win: BrowserWindow) {
  if (process.env.NODE_ENV === 'development') return

  // Close if somehow opened
  win.webContents.on('devtools-opened', () => {
    win.webContents.closeDevTools()
  })

  // Block common shortcuts
  win.webContents.on('before-input-event', (event, input) => {
    const isDevToolsShortcut =
      input.key === 'F12' ||
      (input.control && input.shift && input.key.toLowerCase() === 'i') ||
      (input.meta && input.alt && input.key.toLowerCase() === 'i')
    if (isDevToolsShortcut) {
      event.preventDefault()
    }
  })

  // Block context-menu default
  win.webContents.on('context-menu', () => {
    event.preventDefault()
  })
}

// 新增队列状态相关的IPC处理器
ipcMain.handle('get-queue-status', () => {
  try {
    if (improvedStreamManager && isStreamManagerActive) {
      const status = improvedStreamManager.getStatus()
      console.log('🐾📊 Queue status requested:', status)
      return status
    } else if (audioStreamManager) {
      // 降级到原有的音频流管理器
      return {
        isActive: false,
        audioCapture: { isRunning: false, chunksProcessed: 0, lastChunkTime: 0 },
        transcription: { isConnected: false, totalResults: 0, lastResultTime: 0 },
        aiResponse: { isConnected: false, totalResponses: 0, lastResponseTime: 0 },
        queue: { audioQueue: 0, transcriptionQueue: 0, aiResponseQueue: 0 }
      }
    }
    return null
  } catch (error) {
    console.error('🐾❌ Error getting queue status:', error)
    return null
  }
})

ipcMain.handle('clear-all-queues', async () => {
  try {
    if (improvedStreamManager && isStreamManagerActive) {
      // 清空改进流管理器的队列
      const audioQueue = (improvedStreamManager as any).audioQueue
      if (audioQueue && audioQueue.clearAll) {
        audioQueue.clearAll()
        console.log('🐾🧹 All queues cleared via improved stream manager')
        return true
      }
    }
    return false
  } catch (error) {
    console.error('🐾❌ Error clearing queues:', error)
    return false
  }
})

ipcMain.handle('restart-stream-manager', async () => {
  try {
    console.log('🐾🔄 Restarting stream manager...')
    
    // 停止当前的流管理器
    if (improvedStreamManager) {
      await improvedStreamManager.stop()
      improvedStreamManager = null
      isStreamManagerActive = false
    }
    
    // 重新初始化
    const initialized = await initializeImprovedStreamManager()
    if (initialized) {
      console.log('🐾✅ Stream manager restarted successfully')
      return true
    } else {
      console.log('🐾❌ Failed to restart stream manager')
      return false
    }
  } catch (error) {
    console.error('🐾❌ Error restarting stream manager:', error)
    return false
  }
})
