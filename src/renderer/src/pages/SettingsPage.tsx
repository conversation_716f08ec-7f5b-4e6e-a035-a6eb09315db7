/**
 * ⚙️ 设置页面
 * 集成服务选择器，让用户配置API服务
 */

import React, { useState, useEffect } from 'react'
import ServiceSelector from '../components/ServiceSelector'

// 🐾 修复：使用正确的 ServiceConfig 类型
interface ServiceConfig {
  mode: 'separated' | 'gemini-live'
  separated?: {
    transcription: {
      provider: 'google' | 'assemblyai' | 'deepgram'
      config: {
        apiKey: string
        language?: string
        model?: string
        realtime?: boolean
      }
    }
    ai: {
      provider: 'gemini' | 'groq' | 'together' | 'openai'
      config: {
        apiKey: string
        model?: string
        temperature?: number
        maxTokens?: number
      }
    }
  }
  geminiLive?: {
    apiKey: string
    model?: string
    language?: string
    customPrompt?: string
    profile?: string
  }
}

const SettingsPage: React.FC = () => {
  const [currentConfig, setCurrentConfig] = useState<ServiceConfig | undefined>()
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null)

  useEffect(() => {
    // 加载当前配置
    loadCurrentConfig()

    // 🔧 修复：监听配置更新事件
    const handleConfigUpdate = (event: CustomEvent) => {
      console.log('🔧 SettingsPage: Received config update:', event.detail)
      setCurrentConfig(event.detail)
    }

    window.addEventListener('service-config-updated', handleConfigUpdate as EventListener)

    return () => {
      window.removeEventListener('service-config-updated', handleConfigUpdate as EventListener)
    }
  }, [])

  const loadCurrentConfig = async () => {
    setIsLoading(true)
    try {
      // 🔧 修复：从后端加载当前配置
      if (window.geekAssistant?.getCurrentServiceConfig) {
        const config = await window.geekAssistant.getCurrentServiceConfig()
        if (config) {
          console.log('🔧 SettingsPage: Loaded current config:', config)
          setCurrentConfig(config)
        } else {
          console.log('🔧 SettingsPage: No config found, using default')
        }
      }
    } catch (error) {
      console.error('🔧 SettingsPage: Failed to load config:', error)
      setMessage({
        type: 'error',
        text: '❌ 加载配置失败，请刷新页面重试'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfigChange = async (config: ServiceConfig) => {
    try {
      console.log('🔧 SettingsPage: Config changed:', config)

      // 更新本地状态
      setCurrentConfig(config)

      // 保存配置到后端
      if (window.geekAssistant?.updateServiceConfig) {
        const success = await window.geekAssistant.updateServiceConfig(config)

        if (success) {
          setMessage({
            type: 'success',
            text: '✅ 配置保存成功！'
          })

          // 3秒后清除消息
          setTimeout(() => setMessage(null), 3000)
        } else {
          setMessage({
            type: 'error',
            text: '❌ 配置保存失败，请重试'
          })
        }
      }
    } catch (error) {
      console.error('🔧 SettingsPage: Failed to save config:', error)
      setMessage({
        type: 'error',
        text: `❌ 配置保存失败: ${error instanceof Error ? error.message : '未知错误'}`
      })
    }
  }

  const testCurrentConfig = async () => {
    if (!currentConfig) {
      setMessage({
        type: 'error',
        text: '❌ 请先配置服务后再进行测试'
      })
      return
    }

    setIsLoading(true)
    setMessage({
      type: 'info',
      text: '🧪 正在测试服务连接...'
    })

    try {
      // 这里可以添加实际的服务测试逻辑
      // 暂时模拟测试
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setMessage({
        type: 'success',
        text: '✅ 服务连接测试成功！所有配置的服务都可以正常使用。'
      })
    } catch (error) {
      setMessage({
        type: 'error',
        text: `❌ 服务测试失败: ${error instanceof Error ? error.message : '未知错误'}`
      })
    } finally {
      setIsLoading(false)
    }
  }

  const clearAllConfig = async () => {
    if (confirm('确定要清除所有配置吗？这将删除所有已保存的API密钥。')) {
      try {
        // 🔧 修复：使用后端重置配置
        if (window.geekAssistant?.resetServiceConfig) {
          const success = await window.geekAssistant.resetServiceConfig()

          if (success) {
            setCurrentConfig(undefined)
            setMessage({
              type: 'success',
              text: '🧹 所有配置已清除并重置为默认值'
            })

            // 重新加载配置
            setTimeout(() => {
              loadCurrentConfig()
            }, 1000)
          } else {
            setMessage({
              type: 'error',
              text: '❌ 清除配置失败，请重试'
            })
          }
        }
      } catch (error) {
        console.error('🔧 SettingsPage: Failed to clear config:', error)
        setMessage({
          type: 'error',
          text: `❌ 清除配置失败: ${error instanceof Error ? error.message : '未知错误'}`
        })
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            ⚙️ AI服务配置中心
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            配置你的专属AI服务组合，享受免费、高质量的语音转录和AI对话体验
          </p>
        </div>

        {/* 状态消息 */}
        {message && (
          <div className={`mb-8 p-6 rounded-xl border-l-4 shadow-lg ${
            message.type === 'success'
              ? 'bg-green-50 border-green-500 text-green-800'
              : message.type === 'error'
              ? 'bg-red-50 border-red-500 text-red-800'
              : 'bg-blue-50 border-blue-500 text-blue-800'
          }`}>
            <div className="flex items-center gap-3">
              <div className="text-2xl">
                {message.type === 'success' ? '✅' : message.type === 'error' ? '❌' : 'ℹ️'}
              </div>
              <div className="font-medium">{message.text}</div>
            </div>
          </div>
        )}

        {/* 服务选择器 */}
        <div className="mb-8">
          <ServiceSelector
            currentConfig={currentConfig}
            onConfigChange={handleConfigChange}
          />
        </div>

        {/* 快速操作 */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            🚀 快速操作
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={testCurrentConfig}
              disabled={isLoading || !currentConfig}
              className="p-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all shadow-md"
            >
              <div className="text-xl mb-1">{isLoading ? '🧪' : '🔍'}</div>
              <div className="font-medium">{isLoading ? '测试中...' : '测试连接'}</div>
              <div className="text-xs opacity-90 mt-1">验证API密钥有效性</div>
            </button>

            <button
              onClick={loadCurrentConfig}
              disabled={isLoading}
              className="p-4 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all shadow-md"
            >
              <div className="text-xl mb-1">🔄</div>
              <div className="font-medium">重新加载</div>
              <div className="text-xs opacity-90 mt-1">刷新配置信息</div>
            </button>

            <button
              onClick={clearAllConfig}
              disabled={isLoading}
              className="p-4 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all shadow-md"
            >
              <div className="text-xl mb-1">🧹</div>
              <div className="font-medium">清除配置</div>
              <div className="text-xs opacity-90 mt-1">重置所有设置</div>
            </button>
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            💡 使用提示
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                🎯 推荐组合
              </h4>
              <div className="space-y-2">
                <div className="p-3 bg-white rounded-lg border border-blue-200">
                  <div className="font-medium text-gray-800">🥇 日常使用</div>
                  <div className="text-gray-600">Google Speech + Gemini</div>
                  <div className="text-xs text-gray-500">质量最高，延迟最低</div>
                </div>
                <div className="p-3 bg-white rounded-lg border border-green-200">
                  <div className="font-medium text-gray-800">🥈 经济实惠</div>
                  <div className="text-gray-600">AssemblyAI + Groq</div>
                  <div className="text-xs text-gray-500">免费额度最大</div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-purple-800 mb-3 flex items-center gap-2">
                🔒 安全须知
              </h4>
              <div className="space-y-2 text-gray-600">
                <div className="flex items-start gap-2">
                  <span className="text-green-500 mt-1">✅</span>
                  <span>API密钥安全存储在本地</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-green-500 mt-1">✅</span>
                  <span>支持随时切换服务组合</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-red-500 mt-1">❌</span>
                  <span>不要分享API密钥给他人</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-yellow-500 mt-1">⚠️</span>
                  <span>定期检查API使用量</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-white rounded-lg border border-gray-200">
            <div className="text-center">
              <div className="text-lg font-medium text-gray-800 mb-2">需要帮助？</div>
              <div className="text-gray-600 mb-3">查看完整的API申请和配置指南</div>
              <button
                onClick={() => window.open('docs/FREE_API_COMPLETE_GUIDE.md', '_blank')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                📖 查看完整指南
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
