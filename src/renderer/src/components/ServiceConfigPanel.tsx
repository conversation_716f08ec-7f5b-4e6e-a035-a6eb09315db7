/**
 * AI服务配置面板 - 与项目风格一致的设计
 */

import React, { useState, useEffect } from 'react'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { 
  Settings, 
  Zap, 
  Brain, 
  Mic, 
  ArrowLeft,
  CheckCircle, 
  AlertCircle, 
  Clock,
  Wifi,
  WifiOff,
  Star,
  ArrowRight,
  Play,
  Sparkles,
  Activity,
  Shield
} from 'lucide-react'

interface ServiceConfigPanelProps {
  onClose?: () => void
}

export function ServiceConfigPanel({ onClose }: ServiceConfigPanelProps) {
  const [currentMode, setCurrentMode] = useState<string>('separated')
  const [separatedConfig, setSeparatedConfig] = useState({
    transcription: 'deepgram',
    ai: 'groq'
  })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  // 🔧 修复：移除localStorage加载，统一使用后端配置
  const [testingServices, setTestingServices] = useState<{[key: string]: boolean}>({})
  const [connectionStatus, setConnectionStatus] = useState<{[key: string]: 'connected' | 'disconnected' | 'testing'}>({})

  // 服务配置数据
  const serviceConfigs = {
    modes: [
      {
        id: 'gemini-live',
        name: 'Gemini Live',
        description: 'Google的实时语音AI，一体化解决方案',
        icon: Sparkles,
        features: ['实时对话', '低延迟', '高质量', '稳定可靠'],
        recommended: true,
        status: 'stable'
      },
      {
        id: 'separated',
        name: '分离式服务',
        description: '自由组合转录和AI服务，灵活可定制',
        icon: Settings,
        features: ['高度定制', '服务组合', '成本优化', '性能调优'],
        recommended: false,
        status: 'beta'
      }
    ],
    aiServices: [
      {
        id: 'groq',
        name: 'Groq',
        description: '超高速推理，毫秒级响应',
        icon: Zap,
        features: ['超高速', '低延迟', 'Llama模型'],
        speed: '极快 (<100ms)',
        model: 'Llama-3.1-8B',
        recommended: true
      },
      {
        id: 'together',
        name: 'Together AI',
        description: '多模型托管，强大推理能力',
        icon: Brain,
        features: ['多模型', '高质量', '稳定性好'],
        speed: '快 (~500ms)',
        model: 'Llama-3.3-70B',
        recommended: false
      }
    ]
  }

  // 加载当前配置
  useEffect(() => {
    loadCurrentConfig()
  }, [])

  const loadCurrentConfig = async () => {
    setLoading(true)
    try {
      const config = await (window as any).geekAssistant.getCurrentServiceConfig()
      if (config) {
        setCurrentMode(config.mode || 'gemini-live')
        if (config.separated) {
          setSeparatedConfig({
            transcription: config.separated.transcription?.provider || 'deepgram',
            ai: config.separated.ai?.provider || 'groq'
          })
        }
      }
    } catch (error) {
      console.error('Failed to load service config:', error)
    } finally {
      setLoading(false)
    }
  }

  // 切换服务模式
  const handleModeChange = async (mode: string) => {
    if (mode === currentMode) return
    
    setSaving(true)
    try {
      let config: any = { mode }
      
      if (mode === 'separated') {
        config.separated = {
          transcription: {
            provider: separatedConfig.transcription,
            config: {
              apiKey: 'c068d36f5b37bd1bd64fa168ae5acf0b4c4362ec',
              language: 'zh',
              model: 'nova-2'
            }
          },
          ai: {
            provider: separatedConfig.ai,
            config: {
              apiKey: separatedConfig.ai === 'groq' 
                ? '********************************************************'
                : 'tgp_v1_1tORBOAya6NY3lfRr8tZ1hl7XLQoO_BTj0Lk-lKWcq0',
              model: separatedConfig.ai === 'groq' 
                ? 'llama-3.1-8b-instant' 
                : 'meta-llama/Llama-3.3-70B-Instruct-Turbo',
              temperature: 0.7,
              maxTokens: 500
            }
          }
        }
      } else {
        config.geminiLive = {
          apiKey: 'AIzaSyDxcxP-FViBZOUw6s2Obsji5lllDS1QOiw',
          model: 'gemini-live-2.5-flash-preview',
          language: 'cmn-CN'
        }
      }

      console.log('🔄 ServiceConfigPanel: Switching to mode:', mode, 'with config:', config)

      // 🔧 修复：统一使用后端配置管理，先保存配置再切换服务
      const updateResult = await (window as any).geekAssistant.updateServiceConfig(config)
      if (!updateResult) {
        throw new Error('Failed to save configuration')
      }

      const result = await (window as any).geekAssistant.switchService(config)

      if (result && result.success) {
        setCurrentMode(mode)
        console.log('✅ Service switched successfully to:', mode)
      } else {
        throw new Error(result?.error || 'Service switch failed')
      }
    } catch (error) {
      console.error('❌ Failed to switch service:', error)
      alert('切换服务时发生错误：' + (error as Error).message)
    } finally {
      setSaving(false)
    }
  }

  // 更新分离式服务配置
  const handleSeparatedConfigChange = async (type: 'transcription' | 'ai', provider: string) => {
    const newConfig = {
      ...separatedConfig,
      [type]: provider
    }
    setSeparatedConfig(newConfig)

    // 如果当前是分离式模式，立即应用配置
    if (currentMode === 'separated') {
      setSaving(true)
      try {
        const config = {
          mode: 'separated',
          separated: {
            transcription: {
              provider: newConfig.transcription,
              config: {
                apiKey: 'c068d36f5b37bd1bd64fa168ae5acf0b4c4362ec',
                language: 'zh-CN',
                model: 'nova-2'
              }
            },
            ai: {
              provider: newConfig.ai,
              config: {
                apiKey: newConfig.ai === 'groq' 
                  ? '********************************************************'
                  : 'tgp_v1_1tORBOAya6NY3lfRr8tZ1hl7XLQoO_BTj0Lk-lKWcq0',
                model: newConfig.ai === 'groq' 
                  ? 'llama-3.1-8b-instant' 
                  : 'meta-llama/Llama-3.3-70B-Instruct-Turbo',
                temperature: 0.7,
                maxTokens: 500
              }
            }
          }
        }

        console.log('🔄 ServiceConfigPanel: Updating separated config:', config)

        // 🔧 修复：统一使用后端配置管理，先保存配置再切换服务
        const updateResult = await (window as any).geekAssistant.updateServiceConfig(config)
        if (!updateResult) {
          throw new Error('Failed to save separated configuration')
        }

        const result = await (window as any).geekAssistant.switchService(config)

        if (result && result.success) {
          console.log('✅ Separated config updated successfully')
        } else {
          throw new Error(result?.error || 'Config update failed')
        }
      } catch (error) {
        console.error('❌ Failed to update separated config:', error)
      } finally {
        setSaving(false)
      }
    }
  }

  // 测试服务连接
  const testServiceConnection = async (serviceId: string) => {
    setTestingServices(prev => ({ ...prev, [serviceId]: true }))
    setConnectionStatus(prev => ({ ...prev, [serviceId]: 'testing' }))
    
    try {
      // 模拟测试连接
      await new Promise(resolve => setTimeout(resolve, 2000))
      setConnectionStatus(prev => ({ ...prev, [serviceId]: 'connected' }))
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, [serviceId]: 'disconnected' }))
    } finally {
      setTestingServices(prev => ({ ...prev, [serviceId]: false }))
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-vercel-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-vercel-gray-600">加载配置中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-vercel-white">
      {/* 顶部导航 */}
      <div className="border-b border-vercel-gray-200 bg-white">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {onClose && (
                <button
                  onClick={onClose}
                  className="flex items-center space-x-2 text-vercel-gray-600 hover:text-black transition-colors"
                >
                  <ArrowLeft className="w-5 h-5" />
                  <span>返回</span>
                </button>
              )}
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                  <Settings className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-black">AI服务配置</h1>
                  <p className="text-sm text-vercel-gray-600">选择最适合你的AI服务组合</p>
                </div>
              </div>
            </div>
            
            {saving && (
              <div className="flex items-center text-vercel-gray-600">
                <Clock className="w-4 h-4 mr-2 animate-spin" />
                <span className="text-sm">保存中...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-4xl mx-auto px-6 py-8 space-y-8">
        {/* 当前状态 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5 text-vercel-green-500" />
                <span>当前状态</span>
              </CardTitle>
              <div className="flex items-center space-x-2 text-sm text-vercel-green-600 bg-vercel-green-50 px-3 py-1 rounded-full">
                <Wifi className="w-3 h-3" />
                <span>运行中</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg font-semibold text-black">
                  {currentMode === 'gemini-live' ? 'Gemini Live' : '分离式服务'}
                </div>
                <div className="text-sm text-vercel-gray-600">当前模式</div>
              </div>

              {currentMode === 'separated' && (
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <div className="text-sm font-medium text-black">Deepgram</div>
                    <div className="text-xs text-vercel-gray-600">转录服务</div>
                  </div>
                  <ArrowRight className="w-4 h-4 text-vercel-gray-400" />
                  <div className="text-center">
                    <div className="text-sm font-medium text-black">
                      {separatedConfig.ai === 'groq' ? 'Groq' : 'Together AI'}
                    </div>
                    <div className="text-xs text-vercel-gray-600">AI服务</div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 服务模式选择 */}
        <div>
          <h2 className="text-lg font-semibold text-black mb-4">选择服务模式</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {serviceConfigs.modes.map(mode => {
              const IconComponent = mode.icon
              const isSelected = currentMode === mode.id

              return (
                <Card
                  key={mode.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    isSelected
                      ? 'border-black shadow-md'
                      : 'hover:border-vercel-gray-300'
                  }`}
                  onClick={() => handleModeChange(mode.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      {/* 图标 */}
                      <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                        isSelected ? 'bg-black' : 'bg-vercel-gray-100'
                      }`}>
                        <IconComponent className={`w-6 h-6 ${
                          isSelected ? 'text-white' : 'text-vercel-gray-600'
                        }`} />
                      </div>

                      {/* 内容 */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold text-black">{mode.name}</h3>
                          {mode.recommended && (
                            <div className="flex items-center space-x-1 text-xs text-vercel-yellow-600 bg-vercel-yellow-50 px-2 py-1 rounded-full">
                              <Star className="w-3 h-3" />
                              <span>推荐</span>
                            </div>
                          )}
                          {isSelected && (
                            <CheckCircle className="w-4 h-4 text-vercel-green-500" />
                          )}
                        </div>

                        <p className="text-sm text-vercel-gray-600 mb-3">{mode.description}</p>

                        {/* 特性标签 */}
                        <div className="flex flex-wrap gap-2">
                          {mode.features.map((feature: string) => (
                            <span
                              key={feature}
                              className="text-xs text-vercel-gray-600 bg-vercel-gray-100 px-2 py-1 rounded-md"
                            >
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* 分离式服务详细配置 */}
        {currentMode === 'separated' && (
          <div className="space-y-6">
            {/* 配置流程 */}
            <Card className="bg-vercel-blue-50 border-vercel-blue-200">
              <CardContent className="p-6">
                <div className="flex items-center justify-center space-x-6">
                  <div className="flex items-center space-x-3 bg-white px-4 py-3 rounded-lg border border-vercel-blue-200">
                    <Mic className="w-5 h-5 text-vercel-blue-600" />
                    <div>
                      <div className="font-medium text-black">Deepgram</div>
                      <div className="text-xs text-vercel-gray-600">语音转录</div>
                    </div>
                  </div>

                  <ArrowRight className="w-5 h-5 text-vercel-gray-400" />

                  <div className="flex items-center space-x-3 bg-white px-4 py-3 rounded-lg border border-vercel-blue-200">
                    {separatedConfig.ai === 'groq' ? (
                      <Zap className="w-5 h-5 text-vercel-green-600" />
                    ) : (
                      <Brain className="w-5 h-5 text-vercel-green-600" />
                    )}
                    <div>
                      <div className="font-medium text-black">
                        {separatedConfig.ai === 'groq' ? 'Groq' : 'Together AI'}
                      </div>
                      <div className="text-xs text-vercel-gray-600">AI推理</div>
                    </div>
                  </div>
                </div>

                <div className="text-center mt-4">
                  <p className="text-sm text-vercel-gray-600">
                    专业转录 + 超高速AI推理，响应时间 &lt; 1秒
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* AI服务选择 */}
            <div>
              <h3 className="text-lg font-semibold text-black mb-4">AI推理服务</h3>
              <div className="space-y-3">
                {serviceConfigs.aiServices.map(service => {
                  const IconComponent = service.icon
                  const isSelected = separatedConfig.ai === service.id

                  return (
                    <Card
                      key={service.id}
                      className={`cursor-pointer transition-all duration-200 ${
                        isSelected
                          ? 'border-black shadow-md'
                          : 'hover:border-vercel-gray-300'
                      }`}
                      onClick={() => handleSeparatedConfigChange('ai', service.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-4">
                          {/* 图标 */}
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                            isSelected ? 'bg-black' : 'bg-vercel-gray-100'
                          }`}>
                            <IconComponent className={`w-5 h-5 ${
                              isSelected ? 'text-white' : 'text-vercel-gray-600'
                            }`} />
                          </div>

                          {/* 内容 */}
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h4 className="font-medium text-black">{service.name}</h4>
                              {service.recommended && (
                                <div className="flex items-center space-x-1 text-xs text-vercel-yellow-600 bg-vercel-yellow-50 px-2 py-1 rounded-full">
                                  <Star className="w-3 h-3" />
                                  <span>推荐</span>
                                </div>
                              )}
                              {isSelected && (
                                <CheckCircle className="w-4 h-4 text-vercel-green-500" />
                              )}
                            </div>

                            <p className="text-sm text-vercel-gray-600 mb-2">{service.description}</p>

                            <div className="flex items-center space-x-4 text-xs text-vercel-gray-500">
                              <span>速度: {service.speed}</span>
                              <span>模型: {service.model}</span>
                            </div>
                          </div>

                          {/* 连接测试 */}
                          <div className="flex items-center space-x-2">
                            {connectionStatus[service.id] === 'connected' && (
                              <Wifi className="w-4 h-4 text-vercel-green-500" />
                            )}
                            {connectionStatus[service.id] === 'disconnected' && (
                              <WifiOff className="w-4 h-4 text-vercel-red-500" />
                            )}
                            {connectionStatus[service.id] === 'testing' && (
                              <Clock className="w-4 h-4 text-vercel-blue-500 animate-spin" />
                            )}

                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation()
                                testServiceConnection(service.id)
                              }}
                              disabled={testingServices[service.id]}
                            >
                              {testingServices[service.id] ? '测试中' : '测试'}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          </div>
        )}

        {/* 底部提示 */}
        <div className="text-center">
          <p className="text-sm text-vercel-gray-500">
            💡 配置会自动保存，切换模式后立即生效
          </p>
        </div>
      </div>
    </div>
  )
}
