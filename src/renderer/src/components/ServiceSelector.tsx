/**
 * 🔧 专业的服务配置器
 * 美观、功能完善的API服务配置界面
 */

import React, { useState, useEffect } from 'react'
import CustomAPIManager from './CustomAPIManager'

// 🐾 修复：使用正确的 ServiceConfig 类型
interface ServiceConfig {
  mode: 'separated' | 'gemini-live'
  separated?: {
    transcription: {
      provider: 'google' | 'assemblyai' | 'deepgram' | 'assemblyai'
      config: {
        apiKey: string
        language?: string
        model?: string
        realtime?: boolean
      }
    }
    ai: {
      provider: 'gemini' | 'groq' | 'together' | 'openai' | 'custom'
      config: {
        apiKey: string
        model?: string
        temperature?: number
        maxTokens?: number
        customId?: string  // 自定义API的ID
      }
    }
  }
  geminiLive?: {
    apiKey: string
    model?: string
    language?: string
    customPrompt?: string
    profile?: string
  }
}

interface ServiceSelectorProps {
  onConfigChange: (config: ServiceConfig) => void
  currentConfig?: ServiceConfig
}

// 🐾 简化的内部配置类型
interface InternalConfig {
  transcription: {
    provider: 'google' | 'assemblyai' | 'deepgram' | 'assemblyai'
    apiKey: string
  }
  ai: {
    provider: 'gemini' | 'groq' | 'together' | 'openai' | 'custom'
    apiKey: string
    customId?: string  // 自定义API的ID
  }
}

const SERVICE_INFO = {
  transcription: {
    google: {
      name: 'Google Speech-to-Text',
      freeQuota: '60分钟/月',
      description: '高质量，低延迟，支持125种语言',
      icon: '🎤',
      color: 'blue',
      difficulty: '中等',
      quality: 5,
      speed: 5,
      languages: 125,
      features: ['实时转录', '高准确率', '多语言支持', '标点符号'],
      pros: ['质量最高', '延迟极低', '语言支持最多'],
      cons: ['需要Google账户', '配置稍复杂'],
      setupUrl: 'https://console.cloud.google.com/apis/credentials'
    },
    assemblyai: {
      name: 'AssemblyAI Services',
      freeQuota: '3小时/月',
      description: '功能丰富，支持说话人识别',
      icon: '🎯',
      color: 'purple',
      difficulty: '简单',
      quality: 4,
      speed: 3,
      languages: 50,
      features: ['说话人识别', '情感分析', '实时转录', '多语言'],
      pros: ['功能丰富', '说话人识别', '配置简单'],
      cons: ['需要AssemblyAI账户', '免费额度较少'],
      setupUrl: 'https://www.assemblyai.com/'
    },
    deepgram: {
      name: 'Deepgram',
      freeQuota: '$200额度',
      description: '专业转录，高准确率，Nova-2模型',
      icon: '🌊',
      color: 'teal',
      difficulty: '简单',
      quality: 5,
      speed: 5,
      languages: 30,
      features: ['Nova-2模型', '专业转录', '高准确率', '快速响应'],
      pros: ['专业级质量', '响应极快', '配置简单'],
      cons: ['语言支持有限', '额度消耗较快'],
      setupUrl: 'https://console.deepgram.com/'
    },
    assemblyai: {
      name: 'AssemblyAI',
      freeQuota: '3小时/月',
      description: '专业转录，支持说话人识别',
      icon: '🎯',
      color: 'purple',
      difficulty: '简单',
      quality: 4,
      speed: 3,
      languages: 10,
      features: ['说话人识别', '情感分析', '主题检测', '摘要生成'],
      pros: ['功能最丰富', '说话人识别', '配置简单'],
      cons: ['主要支持英语', '响应较慢'],
      setupUrl: 'https://www.assemblyai.com/'
    }
  },
  ai: {
    gemini: {
      name: 'Google Gemini Live',
      freeQuota: '用户配额',
      description: '实时语音对话，一体化解决方案',
      icon: '✨',
      color: 'blue',
      difficulty: '简单',
      quality: 5,
      speed: 5,
      models: ['gemini-live-2.5-flash'],
      features: ['实时对话', '多模态', '上下文理解', '流式响应'],
      pros: ['质量最高', '实时对话', '一体化方案'],
      cons: ['需要Google账户', '配额限制'],
      setupUrl: 'https://makersuite.google.com/app/apikey'
    },
    groq: {
      name: 'Groq',
      freeQuota: '14,400请求/天',
      description: '超高速推理，毫秒级响应',
      icon: '⚡',
      color: 'yellow',
      difficulty: '简单',
      quality: 4,
      speed: 5,
      models: ['llama-3.1-8b-instant', 'mixtral-8x7b-32768'],
      features: ['超高速推理', '毫秒响应', '大模型支持', '高并发'],
      pros: ['速度最快', '免费额度大', '配置简单'],
      cons: ['模型选择有限', '质量略低于GPT'],
      setupUrl: 'https://console.groq.com/keys'
    },
    together: {
      name: 'Together AI',
      freeQuota: '$25额度',
      description: '多种开源模型，成本低',
      icon: '🤝',
      color: 'green',
      difficulty: '简单',
      quality: 4,
      speed: 4,
      models: ['Llama-3.3-70B', 'Qwen2.5-72B', 'DeepSeek-V3'],
      features: ['开源模型', '多模型选择', '成本优化', '社区驱动'],
      pros: ['模型选择最多', '开源透明', '成本最低'],
      cons: ['质量不稳定', '响应速度一般'],
      setupUrl: 'https://api.together.xyz/settings/api-keys'
    },
    openai: {
      name: 'OpenAI GPT',
      freeQuota: '需充值',
      description: '高质量对话，通用性强',
      icon: '🧠',
      color: 'gray',
      difficulty: '简单',
      quality: 5,
      speed: 4,
      models: ['gpt-4o', 'gpt-4o-mini', 'gpt-3.5-turbo'],
      features: ['高质量对话', '通用性强', '插件支持', '函数调用'],
      pros: ['质量最稳定', '功能最完善', '生态最好'],
      cons: ['需要付费', '成本较高'],
      setupUrl: 'https://platform.openai.com/api-keys'
    }
  }
}

const RECOMMENDED_COMBINATIONS = [
  {
    id: 'default',
    name: '🚀 默认组合',
    subtitle: '当前使用',
    description: '高质量转录，快速AI回复，稳定可靠',
    transcription: 'deepgram',
    ai: 'groq',
    color: 'blue',
    badge: '当前',
    totalQuota: '200小时转录 + 14,400请求/天',
    estimatedUsage: '约可使用90天',
    pros: ['质量优秀', '速度极快', '额度充足'],
    bestFor: '面试练习，日常使用'
  },
  {
    id: 'premium',
    name: '🥇 旗舰组合',
    subtitle: '最佳体验',
    description: '高质量，低延迟，Google生态一体化',
    transcription: 'google',
    ai: 'gemini',
    color: 'green',
    badge: '推荐',
    totalQuota: '60分钟转录 + Gemini配额',
    estimatedUsage: '约可使用30天',
    pros: ['质量最高', '延迟最低', '一体化体验'],
    bestFor: '日常使用，追求最佳体验'
  },
  {
    id: 'economy',
    name: '🥈 经济组合',
    subtitle: '最大额度',
    description: '免费时长最多，响应速度极快',
    transcription: 'assemblyai',
    ai: 'groq',
    color: 'green',
    badge: '省钱',
    totalQuota: '5小时转录 + 14,400请求/天',
    estimatedUsage: '约可使用60天',
    pros: ['额度最大', '速度极快', '企业级稳定'],
    bestFor: '重度使用，预算有限'
  },
  {
    id: 'professional',
    name: '🥉 专业组合',
    subtitle: '功能丰富',
    description: '专业转录功能，多种AI模型选择',
    transcription: 'assemblyai',
    ai: 'together',
    color: 'purple',
    badge: '专业',
    totalQuota: '3小时转录 + $25 AI额度',
    estimatedUsage: '约可使用45天',
    pros: ['功能最丰富', '说话人识别', '模型选择多'],
    bestFor: '专业场景，需要高级功能'
  },
  {
    id: 'speed',
    name: '⚡ 极速组合',
    subtitle: '毫秒响应',
    description: '专业转录 + 超高速AI推理',
    transcription: 'deepgram',
    ai: 'groq',
    color: 'yellow',
    badge: '最快',
    totalQuota: '$200转录额度 + 14,400请求/天',
    estimatedUsage: '约可使用90天',
    pros: ['响应最快', '专业转录', '高并发支持'],
    bestFor: '实时场景，对速度要求极高'
  }
]

export const ServiceSelector: React.FC<ServiceSelectorProps> = ({
  onConfigChange,
  currentConfig
}) => {
  // 🔧 修复：使用内部简化配置进行UI管理，并从currentConfig初始化
  const [config, setConfig] = useState<InternalConfig>(() => {
    // 如果有传入的currentConfig，使用它来初始化
    if (currentConfig) {
      if (currentConfig.mode === 'separated' && currentConfig.separated) {
        return {
          transcription: {
            provider: currentConfig.separated.transcription.provider as any,
            apiKey: currentConfig.separated.transcription.config?.apiKey || ''
          },
          ai: {
            provider: currentConfig.separated.ai.provider as any,
            apiKey: currentConfig.separated.ai.config?.apiKey || ''
          }
        }
      } else if (currentConfig.mode === 'gemini-live' && currentConfig.geminiLive) {
        return {
          transcription: { provider: 'google', apiKey: '' },
          ai: { provider: 'gemini', apiKey: currentConfig.geminiLive.apiKey || '' }
        }
      }
    }

    // 默认配置
    return {
      transcription: { provider: 'deepgram', apiKey: '' },
      ai: { provider: 'groq', apiKey: '' }
    }
  })

  const [activeTab, setActiveTab] = useState<'combinations' | 'custom' | 'advanced'>('combinations')
  const [showApiKeys, setShowApiKeys] = useState(false)
  const [selectedCombination, setSelectedCombination] = useState<string>(() => {
    // 根据当前配置设置默认选中的组合
    if (currentConfig?.mode === 'separated' && currentConfig.separated) {
      const transcription = currentConfig.separated.transcription.provider
      const ai = currentConfig.separated.ai.provider

      // 查找匹配的推荐组合
      const matchingCombination = RECOMMENDED_COMBINATIONS.find(combo =>
        combo.transcription === transcription && combo.ai === ai
      )

      return matchingCombination?.id || 'custom'
    }
    return 'default'
  })
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<Record<string, 'idle' | 'testing' | 'success' | 'error'>>({})
  const [expandedService, setExpandedService] = useState<string | null>(null)

  // 🔧 修复：监听currentConfig变化，更新内部状态
  useEffect(() => {
    if (currentConfig) {
      console.log('🔧 ServiceSelector: currentConfig changed:', currentConfig)

      if (currentConfig.mode === 'separated' && currentConfig.separated) {
        const newConfig = {
          transcription: {
            provider: currentConfig.separated.transcription.provider as any,
            apiKey: currentConfig.separated.transcription.config?.apiKey || ''
          },
          ai: {
            provider: currentConfig.separated.ai.provider as any,
            apiKey: currentConfig.separated.ai.config?.apiKey || ''
          }
        }
        setConfig(newConfig)

        // 更新选中的组合
        const matchingCombination = RECOMMENDED_COMBINATIONS.find(combo =>
          combo.transcription === newConfig.transcription.provider && combo.ai === newConfig.ai.provider
        )
        setSelectedCombination(matchingCombination?.id || 'custom')

      } else if (currentConfig.mode === 'gemini-live' && currentConfig.geminiLive) {
        setConfig({
          transcription: { provider: 'google', apiKey: '' },
          ai: { provider: 'gemini', apiKey: currentConfig.geminiLive.apiKey || '' }
        })
        setSelectedCombination('premium')
      }
    }
  }, [currentConfig])

  // 🐾 修复：构建正确的配置格式
  const buildServiceConfig = (internalConfig: InternalConfig): ServiceConfig => {
    // 如果选择的是 Gemini，使用 Gemini Live 模式
    if (internalConfig.ai.provider === 'gemini') {
      return {
        mode: 'gemini-live',
        geminiLive: {
          apiKey: internalConfig.ai.apiKey || 'AIzaSyDxcxP-FViBZOUw6s2Obsji5lllDS1QOiw',
          model: 'gemini-live-2.5-flash-preview',
          language: 'cmn-CN',
          customPrompt: '',
          profile: 'interview'
        }
      }
    } else {
      // 否则使用分离式服务
      return {
        mode: 'separated',
        separated: {
          transcription: {
            provider: internalConfig.transcription.provider as any,
            config: {
              apiKey: internalConfig.transcription.apiKey || 'b35ec484a76dade61b4554f38a95a6ad33e8fa1a',
              language: 'zh-CN',
              model: internalConfig.transcription.provider === 'deepgram' ? 'nova-2' : undefined,
              realtime: true
            }
          },
          ai: {
            provider: internalConfig.ai.provider as any,
            config: {
              apiKey: internalConfig.ai.apiKey || '********************************************************',
              model: internalConfig.ai.provider === 'groq' ? 'llama-3.1-8b-instant' : 'gpt-3.5-turbo',
              temperature: 0.7,
              maxTokens: 500
            }
          }
        }
      }
    }
  }

  const updateConfig = (newInternalConfig: InternalConfig) => {
    setConfig(newInternalConfig)

    // 构建简单的配置格式
    const serviceConfig = buildServiceConfig(newInternalConfig)

    // 只通知父组件，不保存到 localStorage
    onConfigChange(serviceConfig)
  }

  const applyRecommendedCombination = (combination: typeof RECOMMENDED_COMBINATIONS[0]) => {
    const newConfig = {
      ...config,
      transcription: {
        ...config.transcription,
        provider: combination.transcription as any
      },
      ai: {
        ...config.ai,
        provider: combination.ai as any
      }
    }
    updateConfig(newConfig)
  }

  const updateTranscriptionProvider = (provider: InternalConfig['transcription']['provider']) => {
    const newConfig = {
      ...config,
      transcription: { ...config.transcription, provider }
    }
    updateConfig(newConfig)
  }

  const updateAIProvider = (provider: InternalConfig['ai']['provider']) => {
    const newConfig = {
      ...config,
      ai: { ...config.ai, provider }
    }
    updateConfig(newConfig)
  }

  const updateApiKey = (type: 'transcription' | 'ai', apiKey: string) => {
    updateConfig({
      ...config,
      [type]: { ...config[type], apiKey }
    })
  }

  // 测试单个API连接
  const testApiKey = async (type: 'transcription' | 'ai') => {
    const provider = type === 'transcription' ? config.transcription.provider : config.ai.provider
    const key = `${type === 'ai' ? 'ai-' : ''}${provider}`

    setConnectionStatus(prev => ({ ...prev, [key]: 'testing' }))

    try {
      // 模拟API测试
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 这里应该调用实际的API测试逻辑
      const success = Math.random() > 0.3 // 70%成功率模拟

      setConnectionStatus(prev => ({
        ...prev,
        [key]: success ? 'success' : 'error'
      }))
    } catch (error) {
      setConnectionStatus(prev => ({ ...prev, [key]: 'error' }))
    }
  }

  // 测试所有连接
  const testAllConnections = async () => {
    setIsTestingConnection(true)

    try {
      await Promise.all([
        testApiKey('transcription'),
        testApiKey('ai')
      ])
    } finally {
      setIsTestingConnection(false)
    }
  }

  // 导出配置
  const exportConfig = () => {
    const configToExport = {
      ...config,
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    }

    const blob = new Blob([JSON.stringify(configToExport, null, 2)], {
      type: 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `geek-assistant-config-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // 导入配置
  const importConfig = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'

    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedConfig = JSON.parse(e.target?.result as string)

          // 验证配置格式
          if (importedConfig.transcription && importedConfig.ai) {
            updateConfig(importedConfig)
            alert('配置导入成功！')
          } else {
            alert('配置文件格式不正确')
          }
        } catch (error) {
          alert('配置文件解析失败')
        }
      }
      reader.readAsText(file)
    }

    input.click()
  }

  // 重置配置
  const resetConfig = () => {
    if (confirm('确定要重置所有配置吗？这将清除所有API密钥和设置。')) {
      const defaultConfig = {
        transcription: { provider: 'google' as const, apiKey: '' },
        ai: { provider: 'gemini' as const, apiKey: '' }
      }
      updateConfig(defaultConfig)
      setConnectionStatus({})
      setSelectedCombination('premium')
      alert('配置已重置')
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      {/* 标题栏 */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
        <h3 className="text-xl font-bold text-white flex items-center gap-2">
          🔧 AI服务配置中心
        </h3>
        <p className="text-blue-100 text-sm mt-1">选择最适合你的免费API服务组合</p>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'combinations', name: '推荐组合', icon: '🏆' },
            { id: 'custom', name: '自定义配置', icon: '⚙️' },
            { id: 'advanced', name: '高级设置', icon: '🔬' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="flex items-center gap-2">
                {tab.icon} {tab.name}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      <div className="p-6">
        {activeTab === 'combinations' && renderCombinationsTab()}
        {activeTab === 'custom' && renderCustomTab()}
        {activeTab === 'advanced' && renderAdvancedTab()}
      </div>
    </div>
  )

  // 推荐组合标签页
  function renderCombinationsTab() {
    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h4 className="text-lg font-semibold text-gray-900 mb-2">🎯 为你精选的最佳组合</h4>
          <p className="text-gray-600">根据不同使用场景，我们为你推荐以下免费API组合</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {RECOMMENDED_COMBINATIONS.map((combo) => (
            <div
              key={combo.id}
              className={`relative p-6 rounded-xl border-2 transition-all cursor-pointer hover:shadow-lg ${
                selectedCombination === combo.id
                  ? `border-${combo.color}-500 bg-${combo.color}-50 shadow-md`
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => {
                setSelectedCombination(combo.id)
                applyRecommendedCombination(combo)
              }}
            >
              {/* 徽章 */}
              <div className={`absolute -top-2 -right-2 px-3 py-1 rounded-full text-xs font-bold text-white bg-${combo.color}-500`}>
                {combo.badge}
              </div>

              {/* 标题 */}
              <div className="mb-4">
                <h5 className="text-lg font-bold text-gray-900 mb-1">{combo.name}</h5>
                <p className="text-sm font-medium text-gray-600">{combo.subtitle}</p>
                <p className="text-sm text-gray-500 mt-2">{combo.description}</p>
              </div>

              {/* 服务信息 */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center gap-3">
                  <span className="text-lg">{SERVICE_INFO.transcription[combo.transcription as keyof typeof SERVICE_INFO.transcription].icon}</span>
                  <div>
                    <div className="font-medium text-sm text-gray-900">
                      {SERVICE_INFO.transcription[combo.transcription as keyof typeof SERVICE_INFO.transcription].name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {SERVICE_INFO.transcription[combo.transcription as keyof typeof SERVICE_INFO.transcription].freeQuota}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-lg">{SERVICE_INFO.ai[combo.ai as keyof typeof SERVICE_INFO.ai].icon}</span>
                  <div>
                    <div className="font-medium text-sm text-gray-900">
                      {SERVICE_INFO.ai[combo.ai as keyof typeof SERVICE_INFO.ai].name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {SERVICE_INFO.ai[combo.ai as keyof typeof SERVICE_INFO.ai].freeQuota}
                    </div>
                  </div>
                </div>
              </div>

              {/* 优势和适用场景 */}
              <div className="space-y-2 mb-4">
                <div className="text-xs text-gray-600">
                  <strong>总额度:</strong> {combo.totalQuota}
                </div>
                <div className="text-xs text-gray-600">
                  <strong>预估使用:</strong> {combo.estimatedUsage}
                </div>
                <div className="text-xs text-gray-600">
                  <strong>适合:</strong> {combo.bestFor}
                </div>
              </div>

              {/* 优势标签 */}
              <div className="flex flex-wrap gap-1">
                {combo.pros.map((pro, index) => (
                  <span
                    key={index}
                    className={`px-2 py-1 rounded-full text-xs font-medium bg-${combo.color}-100 text-${combo.color}-700`}
                  >
                    {pro}
                  </span>
                ))}
              </div>

              {/* 选中指示器 */}
              {selectedCombination === combo.id && (
                <div className={`absolute top-4 right-4 w-6 h-6 rounded-full bg-${combo.color}-500 flex items-center justify-center`}>
                  <span className="text-white text-sm">✓</span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 快速配置按钮 */}
        {selectedCombination && (
          <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <h6 className="font-medium text-blue-900">
                  已选择: {RECOMMENDED_COMBINATIONS.find(c => c.id === selectedCombination)?.name}
                </h6>
                <p className="text-sm text-blue-700 mt-1">
                  点击下方按钮获取API密钥申请指南，或切换到"自定义配置"标签页输入密钥
                </p>
              </div>
              <button
                onClick={() => setActiveTab('custom')}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                配置密钥 →
              </button>
            </div>
          </div>
        )}
      </div>
    )
  }

  // 自定义配置标签页
  function renderCustomTab() {
    return (
      <div className="space-y-8">
        <div className="text-center mb-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-2">⚙️ 自定义服务配置</h4>
          <p className="text-gray-600">选择你偏好的转录和AI服务，输入对应的API密钥</p>
        </div>

        {/* 转录服务选择 */}
        <div>
          <h5 className="text-base font-semibold text-gray-900 mb-4 flex items-center gap-2">
            🎤 语音转录服务
          </h5>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {Object.entries(SERVICE_INFO.transcription).map(([key, info]) => (
              <div
                key={key}
                className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                  config.transcription.provider === key
                    ? `border-${info.color}-500 bg-${info.color}-50`
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => updateTranscriptionProvider(key as any)}
              >
                {/* 服务图标和基本信息 */}
                <div className="flex items-start gap-3 mb-3">
                  <span className="text-2xl">{info.icon}</span>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h6 className="font-semibold text-gray-900">{info.name}</h6>
                      {config.transcription.provider === key && (
                        <span className={`px-2 py-1 rounded-full text-xs font-bold text-white bg-${info.color}-500`}>
                          已选择
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{info.description}</p>
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        💰 {info.freeQuota}
                      </span>
                      <span className="flex items-center gap-1">
                        🌍 {info.languages}种语言
                      </span>
                      <span className="flex items-center gap-1">
                        ⚡ {'★'.repeat(info.speed)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 详细信息展开 */}
                <div className="space-y-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      setExpandedService(expandedService === key ? null : key)
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                  >
                    {expandedService === key ? '收起详情 ▲' : '查看详情 ▼'}
                  </button>

                  {expandedService === key && (
                    <div className="mt-3 p-3 bg-white rounded-lg border border-gray-200 space-y-2">
                      <div>
                        <strong className="text-xs text-gray-700">功能特性:</strong>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {info.features.map((feature, idx) => (
                            <span key={idx} className="px-2 py-1 bg-gray-100 text-xs rounded-full">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <strong className="text-xs text-green-700">优势:</strong>
                        <ul className="text-xs text-gray-600 mt-1 list-disc list-inside">
                          {info.pros.map((pro, idx) => (
                            <li key={idx}>{pro}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <strong className="text-xs text-orange-700">注意:</strong>
                        <ul className="text-xs text-gray-600 mt-1 list-disc list-inside">
                          {info.cons.map((con, idx) => (
                            <li key={idx}>{con}</li>
                          ))}
                        </ul>
                      </div>
                      <a
                        href={info.setupUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800"
                      >
                        🔗 申请API密钥
                      </a>
                    </div>
                  )}
                </div>

                {/* 连接状态指示器 */}
                {connectionStatus[key] && (
                  <div className="absolute top-2 right-2">
                    {connectionStatus[key] === 'testing' && (
                      <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                    )}
                    {connectionStatus[key] === 'success' && (
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    )}
                    {connectionStatus[key] === 'error' && (
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* AI对话服务选择 */}
        <div>
          <h5 className="text-base font-semibold text-gray-900 mb-4 flex items-center gap-2">
            🤖 AI对话服务
          </h5>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {Object.entries(SERVICE_INFO.ai).map(([key, info]) => (
              <div
                key={key}
                className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all hover:shadow-md ${
                  config.ai.provider === key
                    ? `border-${info.color}-500 bg-${info.color}-50`
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => updateAIProvider(key as any)}
              >
                {/* 服务图标和基本信息 */}
                <div className="flex items-start gap-3 mb-3">
                  <span className="text-2xl">{info.icon}</span>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h6 className="font-semibold text-gray-900">{info.name}</h6>
                      {config.ai.provider === key && (
                        <span className={`px-2 py-1 rounded-full text-xs font-bold text-white bg-${info.color}-500`}>
                          已选择
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{info.description}</p>
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        💰 {info.freeQuota}
                      </span>
                      <span className="flex items-center gap-1">
                        🧠 {'★'.repeat(info.quality)}
                      </span>
                      <span className="flex items-center gap-1">
                        ⚡ {'★'.repeat(info.speed)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 模型信息 */}
                <div className="mb-2">
                  <div className="text-xs text-gray-600 mb-1">支持模型:</div>
                  <div className="flex flex-wrap gap-1">
                    {info.models.slice(0, 2).map((model, idx) => (
                      <span key={idx} className="px-2 py-1 bg-gray-100 text-xs rounded-full">
                        {model}
                      </span>
                    ))}
                    {info.models.length > 2 && (
                      <span className="px-2 py-1 bg-gray-100 text-xs rounded-full">
                        +{info.models.length - 2}个
                      </span>
                    )}
                  </div>
                </div>

                {/* 详细信息展开 */}
                <div className="space-y-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      setExpandedService(expandedService === `ai-${key}` ? null : `ai-${key}`)
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                  >
                    {expandedService === `ai-${key}` ? '收起详情 ▲' : '查看详情 ▼'}
                  </button>

                  {expandedService === `ai-${key}` && (
                    <div className="mt-3 p-3 bg-white rounded-lg border border-gray-200 space-y-2">
                      <div>
                        <strong className="text-xs text-gray-700">功能特性:</strong>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {info.features.map((feature, idx) => (
                            <span key={idx} className="px-2 py-1 bg-gray-100 text-xs rounded-full">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <strong className="text-xs text-gray-700">所有模型:</strong>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {info.models.map((model, idx) => (
                            <span key={idx} className="px-2 py-1 bg-blue-100 text-xs rounded-full">
                              {model}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <strong className="text-xs text-green-700">优势:</strong>
                        <ul className="text-xs text-gray-600 mt-1 list-disc list-inside">
                          {info.pros.map((pro, idx) => (
                            <li key={idx}>{pro}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <strong className="text-xs text-orange-700">注意:</strong>
                        <ul className="text-xs text-gray-600 mt-1 list-disc list-inside">
                          {info.cons.map((con, idx) => (
                            <li key={idx}>{con}</li>
                          ))}
                        </ul>
                      </div>
                      <a
                        href={info.setupUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800"
                      >
                        🔗 申请API密钥
                      </a>
                    </div>
                  )}
                </div>

                {/* 连接状态指示器 */}
                {connectionStatus[`ai-${key}`] && (
                  <div className="absolute top-2 right-2">
                    {connectionStatus[`ai-${key}`] === 'testing' && (
                      <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                    )}
                    {connectionStatus[`ai-${key}`] === 'success' && (
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    )}
                    {connectionStatus[`ai-${key}`] === 'error' && (
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* API密钥配置 */}
        <div className="bg-gray-50 rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <h5 className="text-base font-semibold text-gray-900 flex items-center gap-2">
              🔑 API密钥配置
            </h5>
            <button
              onClick={() => setShowApiKeys(!showApiKeys)}
              className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors"
            >
              {showApiKeys ? '隐藏密钥' : '显示密钥'}
            </button>
          </div>

          <div className="space-y-4">
            {/* 转录服务API密钥 */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="text-lg">{SERVICE_INFO.transcription[config.transcription.provider].icon}</span>
                <label className="text-sm font-medium text-gray-700">
                  {SERVICE_INFO.transcription[config.transcription.provider].name} API密钥
                </label>
                {config.transcription.apiKey && (
                  <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">已配置</span>
                )}
              </div>
              <div className="relative">
                <input
                  type={showApiKeys ? "text" : "password"}
                  value={config.transcription.apiKey}
                  onChange={(e) => updateApiKey('transcription', e.target.value)}
                  placeholder="输入你的API密钥"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  onClick={() => testApiKey('transcription')}
                  disabled={!config.transcription.apiKey || isTestingConnection}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {connectionStatus[config.transcription.provider] === 'testing' ? '测试中...' : '测试'}
                </button>
              </div>
              <div className="flex items-center justify-between mt-2">
                <a
                  href={SERVICE_INFO.transcription[config.transcription.provider].setupUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                >
                  🔗 申请API密钥
                </a>
                {connectionStatus[config.transcription.provider] === 'success' && (
                  <span className="text-xs text-green-600 flex items-center gap-1">
                    ✅ 连接成功
                  </span>
                )}
                {connectionStatus[config.transcription.provider] === 'error' && (
                  <span className="text-xs text-red-600 flex items-center gap-1">
                    ❌ 连接失败
                  </span>
                )}
              </div>
            </div>

            {/* AI服务API密钥 */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <span className="text-lg">{SERVICE_INFO.ai[config.ai.provider].icon}</span>
                <label className="text-sm font-medium text-gray-700">
                  {SERVICE_INFO.ai[config.ai.provider].name} API密钥
                </label>
                {config.ai.apiKey && (
                  <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">已配置</span>
                )}
              </div>
              <div className="relative">
                <input
                  type={showApiKeys ? "text" : "password"}
                  value={config.ai.apiKey}
                  onChange={(e) => updateApiKey('ai', e.target.value)}
                  placeholder="输入你的API密钥"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  onClick={() => testApiKey('ai')}
                  disabled={!config.ai.apiKey || isTestingConnection}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {connectionStatus[`ai-${config.ai.provider}`] === 'testing' ? '测试中...' : '测试'}
                </button>
              </div>
              <div className="flex items-center justify-between mt-2">
                <a
                  href={SERVICE_INFO.ai[config.ai.provider].setupUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                >
                  🔗 申请API密钥
                </a>
                {connectionStatus[`ai-${config.ai.provider}`] === 'success' && (
                  <span className="text-xs text-green-600 flex items-center gap-1">
                    ✅ 连接成功
                  </span>
                )}
                {connectionStatus[`ai-${config.ai.provider}`] === 'error' && (
                  <span className="text-xs text-red-600 flex items-center gap-1">
                    ❌ 连接失败
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* 批量测试按钮 */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={testAllConnections}
              disabled={!config.transcription.apiKey || !config.ai.apiKey || isTestingConnection}
              className="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-400 disabled:cursor-not-allowed transition-all font-medium"
            >
              {isTestingConnection ? '🧪 测试连接中...' : '🧪 测试所有连接'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  // 高级设置标签页
  function renderAdvancedTab() {
    return (
      <div className="space-y-6">
        <div className="text-center mb-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-2">🔬 高级设置</h4>
          <p className="text-gray-600">配置高级选项和查看详细信息</p>
        </div>

        {/* 当前配置概览 */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
          <h5 className="text-base font-semibold text-gray-900 mb-4">📊 当前配置概览</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h6 className="font-medium text-gray-700 mb-2 flex items-center gap-2">
                🎤 转录服务
              </h6>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center gap-3 mb-2">
                  <span className="text-xl">{SERVICE_INFO.transcription[config.transcription.provider].icon}</span>
                  <div>
                    <div className="font-medium text-sm">{SERVICE_INFO.transcription[config.transcription.provider].name}</div>
                    <div className="text-xs text-gray-500">{SERVICE_INFO.transcription[config.transcription.provider].freeQuota}</div>
                  </div>
                </div>
                <div className="space-y-1 text-xs text-gray-600">
                  <div>质量: {'★'.repeat(SERVICE_INFO.transcription[config.transcription.provider].quality)}</div>
                  <div>速度: {'★'.repeat(SERVICE_INFO.transcription[config.transcription.provider].speed)}</div>
                  <div>语言: {SERVICE_INFO.transcription[config.transcription.provider].languages}种</div>
                  <div>密钥: {config.transcription.apiKey ? '✅ 已配置' : '❌ 未配置'}</div>
                </div>
              </div>
            </div>

            <div>
              <h6 className="font-medium text-gray-700 mb-2 flex items-center gap-2">
                🤖 AI服务
              </h6>
              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <div className="flex items-center gap-3 mb-2">
                  <span className="text-xl">{SERVICE_INFO.ai[config.ai.provider].icon}</span>
                  <div>
                    <div className="font-medium text-sm">{SERVICE_INFO.ai[config.ai.provider].name}</div>
                    <div className="text-xs text-gray-500">{SERVICE_INFO.ai[config.ai.provider].freeQuota}</div>
                  </div>
                </div>
                <div className="space-y-1 text-xs text-gray-600">
                  <div>质量: {'★'.repeat(SERVICE_INFO.ai[config.ai.provider].quality)}</div>
                  <div>速度: {'★'.repeat(SERVICE_INFO.ai[config.ai.provider].speed)}</div>
                  <div>模型: {SERVICE_INFO.ai[config.ai.provider].models.length}个</div>
                  <div>密钥: {config.ai.apiKey ? '✅ 已配置' : '❌ 未配置'}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 配置导入导出 */}
        <div className="bg-white rounded-xl p-6 border border-gray-200">
          <h5 className="text-base font-semibold text-gray-900 mb-4">💾 配置管理</h5>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={exportConfig}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <div className="text-2xl mb-2">📤</div>
              <div className="font-medium text-sm">导出配置</div>
              <div className="text-xs text-gray-500 mt-1">保存当前配置到文件</div>
            </button>

            <button
              onClick={importConfig}
              className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors text-center"
            >
              <div className="text-2xl mb-2">📥</div>
              <div className="font-medium text-sm">导入配置</div>
              <div className="text-xs text-gray-500 mt-1">从文件恢复配置</div>
            </button>

            <button
              onClick={resetConfig}
              className="p-4 border border-red-300 rounded-lg hover:bg-red-50 transition-colors text-center text-red-600"
            >
              <div className="text-2xl mb-2">🗑️</div>
              <div className="font-medium text-sm">重置配置</div>
              <div className="text-xs text-red-500 mt-1">清除所有设置</div>
            </button>
          </div>
        </div>

        {/* 自定义API管理 */}
        <CustomAPIManager
          onAPIChange={(apis) => {
            console.log('Custom APIs updated:', apis)
            // 这里可以处理自定义API变化的逻辑
          }}
          className="mb-6"
        />

        {/* 使用统计 */}
        <div className="bg-white rounded-xl p-6 border border-gray-200">
          <h5 className="text-base font-semibold text-gray-900 mb-4">📈 使用统计</h5>
          <div className="text-sm text-gray-600">
            <p>此功能将在未来版本中提供，用于跟踪API使用量和成本。</p>
          </div>
        </div>
      </div>
    )
  }

}

export default ServiceSelector
